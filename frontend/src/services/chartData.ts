import { httpClient } from './request'
import { FinancialService } from './financial'
import type {
  FinancialStatsRequest,
  GroupedFinancialStatsResponse
} from '@/types'
import type {
  RevenueDataPoint,
  BusinessStructureData,
  HeatmapDataPoint,
  RechargeDistributionData,
  BoxConsumptionData,
  AgentVsAnchorData,
  UserBehaviorScatterData,
  PopularBoxData,
  ItemRarityData
} from '@/components/charts'

/**
 * 图表数据服务
 * 整合多数据源，提供统一的图表数据接口
 */
export class ChartDataService {
  /**
   * 获取收入趋势数据
   */
  static async getRevenueTrendData(request: FinancialStatsRequest): Promise<RevenueDataPoint[]> {
    try {
      // 获取基础财务统计数据
      const financialData = await FinancialService.getFinancialStats(request)
      
      // 模拟生成时间序列数据（实际应该从后端获取）
      const mockData: RevenueDataPoint[] = []
      const startDate = new Date(request.startTime)
      const endDate = new Date(request.endTime)
      const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
      
      for (let i = 0; i < daysDiff; i++) {
        const date = new Date(startDate)
        date.setDate(date.getDate() + i)
        
        mockData.push({
          date: date.toISOString().split('T')[0],
          rechargeRevenue: Math.random() * 10000 + 5000,
          boxRevenue: Math.random() * 15000 + 8000,
          commissionExpense: Math.random() * 3000 + 1000,
          netRevenue: Math.random() * 20000 + 10000,
          timestamp: date.getTime()
        })
      }
      
      return mockData
    } catch (error) {
      console.error('获取收入趋势数据失败:', error)
      return []
    }
  }

  /**
   * 获取业务结构数据
   */
  static async getBusinessStructureData(request: FinancialStatsRequest): Promise<BusinessStructureData[]> {
    try {
      const financialData = await FinancialService.getFinancialStats(request)
      
      // 基于财务统计数据生成业务结构数据
      const mockData: BusinessStructureData[] = [
        {
          name: '充值业务',
          value: 198234.15,
          percentage: 45.2,
          color: '#3b82f6'
        },
        {
          name: '开箱业务',
          value: 559796.30,
          percentage: 52.1,
          color: '#10b981'
        },
        {
          name: '佣金支出',
          value: 2742.11,
          percentage: 2.7,
          color: '#f59e0b'
        }
      ]
      
      return mockData
    } catch (error) {
      console.error('获取业务结构数据失败:', error)
      return []
    }
  }

  /**
   * 获取用户活跃度热力图数据
   */
  static async getUserActivityHeatmapData(request: FinancialStatsRequest): Promise<HeatmapDataPoint[]> {
    try {
      const mockData: HeatmapDataPoint[] = []
      const startDate = new Date(request.startTime)
      const endDate = new Date(request.endTime)
      const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
      
      for (let day = 0; day < Math.min(daysDiff, 7); day++) {
        const date = new Date(startDate)
        date.setDate(date.getDate() + day)
        const dateStr = date.toISOString().split('T')[0]
        
        for (let hour = 0; hour < 24; hour++) {
          // 模拟用户活跃度模式：白天较高，夜晚较低
          const baseActivity = hour >= 8 && hour <= 22 ? 50 : 10
          const randomFactor = Math.random() * 0.5 + 0.75
          const activeUsers = Math.round(baseActivity * randomFactor)
          
          mockData.push({
            date: dateStr,
            hour,
            activeUsers,
            transactionAmount: activeUsers * (Math.random() * 100 + 50),
            label: `${dateStr} ${hour.toString().padStart(2, '0')}:00`
          })
        }
      }
      
      return mockData
    } catch (error) {
      console.error('获取用户活跃度数据失败:', error)
      return []
    }
  }

  /**
   * 获取充值分布数据
   */
  static async getRechargeDistributionData(): Promise<RechargeDistributionData[]> {
    try {
      // 模拟充值分布数据
      const mockData: RechargeDistributionData[] = [
        {
          range: '0-50元',
          orderCount: 245,
          userCount: 198,
          totalAmount: 8750.50,
          avgAmount: 35.71,
          minAmount: 10,
          maxAmount: 50
        },
        {
          range: '50-100元',
          orderCount: 387,
          userCount: 312,
          totalAmount: 29845.75,
          avgAmount: 77.15,
          minAmount: 50,
          maxAmount: 100
        },
        {
          range: '100-200元',
          orderCount: 456,
          userCount: 398,
          totalAmount: 68234.20,
          avgAmount: 149.64,
          minAmount: 100,
          maxAmount: 200
        },
        {
          range: '200-500元',
          orderCount: 298,
          userCount: 267,
          totalAmount: 89567.30,
          avgAmount: 300.56,
          minAmount: 200,
          maxAmount: 500
        },
        {
          range: '500元以上',
          orderCount: 143,
          userCount: 128,
          totalAmount: 98456.40,
          avgAmount: 688.51,
          minAmount: 500,
          maxAmount: 2000
        }
      ]
      
      return mockData
    } catch (error) {
      console.error('获取充值分布数据失败:', error)
      return []
    }
  }

  /**
   * 获取开箱消费趋势数据
   */
  static async getBoxConsumptionData(request: FinancialStatsRequest): Promise<BoxConsumptionData[]> {
    try {
      const mockData: BoxConsumptionData[] = []
      const startDate = new Date(request.startTime)
      const endDate = new Date(request.endTime)
      const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
      
      for (let i = 0; i < daysDiff; i++) {
        const date = new Date(startDate)
        date.setDate(date.getDate() + i)
        
        const boxCount = Math.round(Math.random() * 500 + 200)
        const avgPrice = Math.random() * 50 + 30
        const boxAmount = boxCount * avgPrice
        const itemValue = boxAmount * (Math.random() * 0.4 + 0.8) // 80-120%的回报率
        
        mockData.push({
          date: date.toISOString().split('T')[0],
          boxCount,
          boxAmount,
          itemValue,
          userCount: Math.round(boxCount * 0.3), // 假设每个用户平均开3个箱子
          timestamp: date.getTime()
        })
      }
      
      return mockData
    } catch (error) {
      console.error('获取开箱消费数据失败:', error)
      return []
    }
  }

  /**
   * 获取代理vs主播对比数据
   */
  static async getAgentVsAnchorData(): Promise<AgentVsAnchorData[]> {
    try {
      const mockData: AgentVsAnchorData[] = [
        {
          category: '本月',
          agent: {
            count: 5,
            totalRevenue: 4968.81,
            avgRevenue: 993.76,
            commission: 496.89,
            avgCommissionRate: 10.0
          },
          anchor: {
            count: 49,
            totalRevenue: 24591.37,
            avgRevenue: 501.86,
            commission: 2505.14,
            avgCommissionRate: 9.96
          }
        },
        {
          category: '上月',
          agent: {
            count: 4,
            totalRevenue: 3856.42,
            avgRevenue: 964.11,
            commission: 385.64,
            avgCommissionRate: 10.0
          },
          anchor: {
            count: 52,
            totalRevenue: 26789.55,
            avgRevenue: 515.18,
            commission: 2678.96,
            avgCommissionRate: 10.0
          }
        }
      ]
      
      return mockData
    } catch (error) {
      console.error('获取代理vs主播数据失败:', error)
      return []
    }
  }

  /**
   * 获取用户行为散点图数据
   */
  static async getUserBehaviorScatterData(): Promise<UserBehaviorScatterData[]> {
    try {
      const mockData: UserBehaviorScatterData[] = []
      
      // 生成模拟用户数据
      for (let i = 1; i <= 100; i++) {
        const rechargeCount = Math.round(Math.random() * 20 + 1)
        const avgAmount = Math.random() * 300 + 50
        const totalAmount = rechargeCount * avgAmount
        const userLevel = Math.min(Math.floor(totalAmount / 1000) + 1, 10)
        
        let userType: 'normal' | 'vip' | 'premium' = 'normal'
        if (totalAmount > 5000) userType = 'premium'
        else if (totalAmount > 2000) userType = 'vip'
        
        mockData.push({
          userId: i,
          userName: `用户${i.toString().padStart(3, '0')}`,
          rechargeCount,
          totalAmount,
          avgAmount,
          userLevel,
          userType,
          activityScore: Math.round(Math.random() * 100 + 50),
          lastRechargeTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString()
        })
      }
      
      return mockData
    } catch (error) {
      console.error('获取用户行为数据失败:', error)
      return []
    }
  }

  /**
   * 获取热门箱子排行数据
   */
  static async getPopularBoxData(): Promise<PopularBoxData[]> {
    try {
      const boxNames = [
        '犹豫就白给', '参与对战活动', '新手福利箱', '每日签到箱', '限时特惠箱',
        '高级武器箱', '稀有皮肤箱', '传说装备箱', '节日庆典箱', '周年纪念箱',
        '竞技场奖励箱', '任务完成箱', '充值返利箱', 'VIP专属箱', '神秘宝箱'
      ]
      
      const mockData: PopularBoxData[] = boxNames.map((name, index) => {
        const openCount = Math.round(Math.random() * 5000 + 1000)
        const avgPrice = Math.random() * 100 + 20
        const revenue = openCount * avgPrice
        const userCount = Math.round(openCount * 0.4)
        
        return {
          boxId: index + 1,
          boxName: name,
          openCount,
          revenue,
          userCount,
          avgPrice,
          popularityScore: Math.round(Math.random() * 100 + 50),
          boxType: index < 5 ? 'common' : index < 10 ? 'rare' : 'legendary',
          launchTime: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString()
        }
      })
      
      return mockData.sort((a, b) => b.openCount - a.openCount)
    } catch (error) {
      console.error('获取热门箱子数据失败:', error)
      return []
    }
  }

  /**
   * 获取物品稀有度分布数据
   */
  static async getItemRarityData(): Promise<ItemRarityData[]> {
    try {
      const mockData: ItemRarityData[] = [
        {
          rarityLevel: 1,
          rarityName: '普通',
          itemCount: 1250,
          totalValue: 125000,
          avgValue: 100,
          obtainCount: 15600,
          color: '#94a3b8'
        },
        {
          rarityLevel: 2,
          rarityName: '不常见',
          itemCount: 856,
          totalValue: 171200,
          avgValue: 200,
          obtainCount: 8940,
          color: '#22c55e'
        },
        {
          rarityLevel: 3,
          rarityName: '稀有',
          itemCount: 432,
          totalValue: 216000,
          avgValue: 500,
          obtainCount: 3456,
          color: '#3b82f6'
        },
        {
          rarityLevel: 4,
          rarityName: '史诗',
          itemCount: 156,
          totalValue: 156000,
          avgValue: 1000,
          obtainCount: 987,
          color: '#a855f7'
        },
        {
          rarityLevel: 5,
          rarityName: '传说',
          itemCount: 45,
          totalValue: 135000,
          avgValue: 3000,
          obtainCount: 234,
          color: '#f59e0b'
        },
        {
          rarityLevel: 6,
          rarityName: '神话',
          itemCount: 12,
          totalValue: 120000,
          avgValue: 10000,
          obtainCount: 45,
          color: '#ef4444'
        }
      ]
      
      return mockData
    } catch (error) {
      console.error('获取物品稀有度数据失败:', error)
      return []
    }
  }
}

export default ChartDataService
