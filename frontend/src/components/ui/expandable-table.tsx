import React, { useState } from 'react'
import { ChevronDown, ChevronRight, Loader2 } from 'lucide-react'
import { Badge } from './badge'
import { Checkbox } from './checkbox'
import { OrderService } from '@/services/financial.ts'
import { preciseCalculation } from '@/utils/financial'
import type { OrderVO } from '@/types'

interface ExpandableTableProps {
  orders: OrderVO[]
  selectedOrderIds: string[]
  onCheckboxChange: (payid: string, checked: boolean) => void
  onSelectAll: (checked: boolean) => void
  getStatusBadgeVariant: (status: number) => "success" | "default" | "warning" | "info" | "destructive" | "outline" | "secondary"
  onViewDetail: (order: OrderVO) => void
  subOrdersMap: Map<string, OrderVO[]>
  onSubOrdersMapChange: (map: Map<string, OrderVO[]>) => void
  getOrderSelectionState: (orderId: string) => { selected: boolean; indeterminate: boolean }
}

export const ExpandableTable: React.FC<ExpandableTableProps> = ({
  orders,
  selectedOrderIds,
  onCheckboxChange,
  onSelectAll,
  getStatusBadgeVariant,
  onViewDetail,
  subOrdersMap,
  onSubOrdersMapChange,
  getOrderSelectionState
}) => {
  const [expandedOrders, setExpandedOrders] = useState<Set<string>>(new Set())
  const [loadingSubOrders, setLoadingSubOrders] = useState<Set<string>>(new Set())
  // 现在orders只包含主订单
  const mainOrders = orders

  /**
   * 计算主订单的总充值（包括所有子订单）
   */
  const calculateTotalRecharge = (mainOrder: OrderVO): number => {
    if (!mainOrder.isMainOrder) {
      return mainOrder.totalAmount
    }
    // 主订单的总充值金额计算
    return mainOrder.totalAmount
  }

  /**
   * 计算主订单的总佣金（包括所有子订单）
   */
  const calculateTotalCommission = (mainOrder: OrderVO): number => {
    if (!mainOrder.isMainOrder) {
      return mainOrder.feeActual
    }
    // 主订单的佣金
    let totalCommission = mainOrder.feeActual

    // 加上所有子订单的佣金
    const subOrders = subOrdersMap.get(mainOrder.payid)
    if (subOrders && subOrders.length > 0) {
      subOrders.forEach(subOrder => {
        totalCommission = preciseCalculation.add(totalCommission, subOrder.feeActual)
      })
    }

    return totalCommission
  }

  /**
   * 计算主订单的总充值金额（包括所有子订单）
   */
  const calculateTotalAmount = (mainOrder: OrderVO): number => {
    if (!mainOrder.isMainOrder) {
      return mainOrder.totalAmount
    }

    // 主订单的充值金额
    let totalAmount = mainOrder.totalAmount

    // 加上所有子订单的充值金额
    const subOrders = subOrdersMap.get(mainOrder.payid)
    if (subOrders && subOrders.length > 0) {
      subOrders.forEach(subOrder => {
        totalAmount = preciseCalculation.add(totalAmount, subOrder.totalAmount)
      })
    }

    return totalAmount
  }

  const toggleExpand = async (payid: string) => {
    const newExpanded = new Set(expandedOrders)

    if (newExpanded.has(payid)) {
      // 收起
      newExpanded.delete(payid)
    } else {
      // 展开 - 需要加载子订单
      newExpanded.add(payid)

      // 如果还没有加载过子订单，则加载
      if (!subOrdersMap.has(payid)) {
        setLoadingSubOrders(prev => new Set(prev).add(payid))

        try {
          const subOrders = await OrderService.getSubOrders(payid)
          const newMap = new Map(subOrdersMap).set(payid, subOrders)
          onSubOrdersMapChange(newMap)
        } catch (error) {
          console.error('加载子订单失败:', error)
          // 加载失败时收起
          newExpanded.delete(payid)
        } finally {
          setLoadingSubOrders(prev => {
            const newSet = new Set(prev)
            newSet.delete(payid)
            return newSet
          })
        }
      }
    }

    setExpandedOrders(newExpanded)
  }

  const renderOrderRow = (order: OrderVO, isSubOrder = false, level = 0) => {
    const hasSubOrders = !isSubOrder && order.isMainOrder // 主订单都可能有子订单
    const isExpanded = expandedOrders.has(order.payid)
    const isLoading = loadingSubOrders.has(order.payid)
    const indentClass = level > 0 ? `pl-${4 + level * 4}` : ''
    const bgClass = isSubOrder ? 'bg-gray-50' : 'bg-white hover:bg-gray-50'

    return (
      <tr key={order.payid} className={bgClass}>
        <td className={`px-4 py-3 ${indentClass}`}>
          <div className="flex items-center gap-2">
            {hasSubOrders ? (
              <button
                onClick={() => toggleExpand(order.payid)}
                className="p-1 hover:bg-gray-200 rounded transition-colors"
                aria-label={isExpanded ? "收起" : "展开"}
                disabled={isLoading}
              >
                {isLoading ? (
                  <Loader2 className="w-4 h-4 text-gray-600 animate-spin" />
                ) : isExpanded ? (
                  <ChevronDown className="w-4 h-4 text-gray-600" />
                ) : (
                  <ChevronRight className="w-4 h-4 text-gray-600" />
                )}
              </button>
            ) : (
              <div className="w-6" /> // 占位符，保持对齐
            )}
            <Checkbox
              checked={(() => {
                const state = getOrderSelectionState(order.payid)
                return state.selected
              })()}
              indeterminate={(() => {
                const state = getOrderSelectionState(order.payid)
                return state.indeterminate
              })()}
              onCheckedChange={(checked) => onCheckboxChange(order.payid, checked === true)}
            />
          </div>
        </td>

        {/* 订单信息 */}
        <td className="px-4 py-3">
          <div className="space-y-1">
            <div className="font-medium text-gray-900">{order.payid}</div>
            {order.parentsPayid && (
              <div className="text-sm text-gray-500">
                父订单: {order.parentsPayid}
              </div>
            )}
            <div className="flex items-center gap-2">
              {order.isMainOrder ? (
                <Badge variant="default">主订单</Badge>
              ) : (
                <Badge variant="secondary">子订单</Badge>
              )}
              {hasSubOrders && subOrdersMap.has(order.payid) && (
                <Badge variant="outline">
                  {subOrdersMap.get(order.payid)?.length || 0}个子订单
                </Badge>
              )}
            </div>
          </div>
        </td>

        {/* 用户信息 */}
        <td className="px-4 py-3">
          <div className="space-y-1">
            {order.agent && (
              <div className="text-sm">
                <span className="text-gray-500">代理:</span>
                <span className="ml-1 font-medium">
                  {order.agentNickname || order.agent}
                </span>
              </div>
            )}
            {order.anchor && (
              <div className="text-sm">
                <span className="text-gray-500">主播:</span>
                <span className="ml-1 font-medium">
                  {order.anchorNickname || order.anchor}
                </span>
              </div>
            )}
            <div className="text-sm text-gray-500">
              收款人: {order.feePerson}
            </div>
          </div>
        </td>

        {/* 金额信息 */}
        <td className="px-4 py-3 text-right">
          <div className="space-y-1">
            <div className="font-medium text-gray-900">
              {order.isMainOrder ? (
                <>
                  {OrderService.formatAmount(calculateTotalAmount(order))}
                  {subOrdersMap.has(order.payid) && subOrdersMap.get(order.payid)!.length > 0 && (
                    <div className="text-xs text-blue-600">
                      (含{subOrdersMap.get(order.payid)!.length}个子订单)
                    </div>
                  )}
                </>
              ) : (
                OrderService.formatAmount(order.totalAmount)
              )}
            </div>
            <div className="text-sm text-gray-500">
              {order.isMainOrder ? (
                <>
                  {order.actualSettlementAmount > 0 ? (
                    <>实际结算: {OrderService.formatAmount(order.actualSettlementAmount)}</>
                  ) : (
                    <>佣金合计: {OrderService.formatAmount(calculateTotalCommission(order))}</>
                  )}
                </>
              ) : (
                <>
                  {order.actualSettlementAmount > 0 ? (
                    <>实际结算: {OrderService.formatAmount(order.actualSettlementAmount)}</>
                  ) : (
                    <>佣金: {OrderService.formatAmount(order.feeActual)}</>
                  )}
                </>
              )}
            </div>
            <div className="text-sm text-gray-500">
              比例: {order.feeRate}%
            </div>
          </div>
        </td>

        {/* 状态 */}
        <td className="px-4 py-3 text-center">
          <div className="space-y-1">
            <Badge variant={getStatusBadgeVariant(order.orderStatus)}>
              {order.orderStatusDesc}
            </Badge>
            <div className="text-xs text-gray-500">
              {order.settlementStatusDesc}
            </div>
          </div>
        </td>

        {/* 时间信息 */}
        <td className="px-4 py-3 text-center">
          <div className="space-y-2">
            {/* 结算时间区间 - 主要信息 */}
            {order.settlementStartTime && order.settlementEndTime ? (
              <div className="space-y-1">
                <div className="text-sm text-gray-900 font-medium">
                  {OrderService.formatTimestamp(order.settlementStartTime)}
                </div>
                <div className="text-xs text-gray-500">至</div>
                <div className="text-sm text-gray-900 font-medium">
                  {OrderService.formatTimestamp(order.settlementEndTime)}
                </div>
              </div>
            ) : (
              <div className="space-y-1">
                <div className="text-sm font-semibold text-gray-500">
                  结算时间区间
                </div>
                <div className="text-xs text-gray-400">
                  未设置结算时间
                </div>
              </div>
            )}
          </div>
        </td>
        <td>
          {/* 创建时间 - 次要信息 */}
          <div className="pt-2 border-gray-100">
            <div className="text-xs text-gray-600">
              {OrderService.formatTimestamp(order.createTime)}
            </div>
          </div>
        </td>
        {/* 操作 */}
        <td className="px-4 py-3 text-center">
          <div className="flex items-center justify-center gap-2">
            <button
              onClick={() => onViewDetail(order)}
              className="text-blue-600 hover:text-blue-800 text-sm"
            >
              查看
            </button>
          </div>
        </td>
      </tr>
    )
  }

  const renderExpandableRows = () => {
    const rows: React.ReactElement[] = []
    
    mainOrders.forEach(mainOrder => {
      // 渲染主订单
      rows.push(renderOrderRow(mainOrder))
      
      // 如果展开且有子订单，渲染子订单
      if (expandedOrders.has(mainOrder.payid) && subOrdersMap.has(mainOrder.payid)) {
        const subOrders = subOrdersMap.get(mainOrder.payid)!
        subOrders.forEach(subOrder => {
          rows.push(renderOrderRow(subOrder, true, 1))
        })
      }
    })
    
    return rows
  }

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              <Checkbox
                checked={(() => {
                  if (orders.length === 0) return false

                  // 计算所有可选择的订单数量（包括子订单）
                  let totalSelectableOrders = orders.length
                  subOrdersMap.forEach((subOrders) => {
                    totalSelectableOrders += subOrders.length
                  })

                  return selectedOrderIds.length === totalSelectableOrders
                })()}
                indeterminate={(() => {
                  if (orders.length === 0 || selectedOrderIds.length === 0) return false

                  // 计算所有可选择的订单数量（包括子订单）
                  let totalSelectableOrders = orders.length
                  subOrdersMap.forEach((subOrders) => {
                    totalSelectableOrders += subOrders.length
                  })

                  return selectedOrderIds.length > 0 && selectedOrderIds.length < totalSelectableOrders
                })()}
                onCheckedChange={(checked) => onSelectAll(checked === true)}
              />
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              订单信息
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              用户信息
            </th>
            <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              金额信息
            </th>
            <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
              状态
            </th>
            <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
              结算时间区间
            </th>
            <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
              创建时间
            </th>
            <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
              操作
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {renderExpandableRows()}
        </tbody>
      </table>
    </div>
  )
}
