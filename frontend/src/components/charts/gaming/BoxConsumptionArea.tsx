import React, { useMemo } from 'react'
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,

} from 'recharts'
import { BaseChart, type BaseChartProps } from '../base/BaseChart'
import { ChartContainer } from '../base/ChartContainer'
import { CHART_COLORS, CHART_ANIMATIONS } from '../utils/chartTheme'
import { formatters } from '../utils/chartDataTransform'
import { TrendingUp, TrendingDown, Package } from 'lucide-react'

/**
 * 开箱消费数据接口
 */
export interface BoxConsumptionData {
  /** 日期 */
  date: string
  /** 开箱次数 */
  boxCount: number
  /** 开箱金额 */
  boxAmount: number
  /** 获得物品价值 */
  itemValue: number
  /** 参与用户数 */
  userCount: number
  /** 时间戳 */
  timestamp?: number
}

/**
 * 开箱消费趋势面积图属性接口
 */
export interface BoxConsumptionAreaProps extends Omit<BaseChartProps, 'children'> {
  /** 开箱消费数据 */
  data: BoxConsumptionData[]
  /** 显示的数据系列 */
  series?: {
    boxCount?: boolean
    boxAmount?: boolean
    itemValue?: boolean
    userCount?: boolean
  }
  /** 是否显示网格 */
  showGrid?: boolean
  /** 图表高度 */
  height?: number
  /** 是否启用动画 */
  animated?: boolean
  /** 是否显示趋势分析 */
  showTrend?: boolean
  /** 自定义颜色配置 */
  colors?: {
    boxCount?: string
    boxAmount?: string
    itemValue?: string
    userCount?: string
  }
}

/**
 * 默认系列配置
 */
const DEFAULT_SERIES = {
  boxCount: true,
  boxAmount: true,
  itemValue: true,
  userCount: false
}

/**
 * 默认颜色配置
 */
const DEFAULT_COLORS = {
  boxCount: CHART_COLORS.primary,
  boxAmount: CHART_COLORS.success,
  itemValue: CHART_COLORS.warning,
  userCount: CHART_COLORS.info
}

/**
 * 系列配置
 */
const SERIES_CONFIG = [
  {
    key: 'boxCount',
    name: '开箱次数',
    yAxisId: 'count',
    formatter: formatters.number
  },
  {
    key: 'boxAmount',
    name: '开箱金额',
    yAxisId: 'amount',
    formatter: formatters.currency
  },
  {
    key: 'itemValue',
    name: '物品价值',
    yAxisId: 'amount',
    formatter: formatters.currency
  },
  {
    key: 'userCount',
    name: '参与用户',
    yAxisId: 'count',
    formatter: formatters.number
  }
]

/**
 * 开箱消费趋势面积图组件
 */
export const BoxConsumptionArea: React.FC<BoxConsumptionAreaProps> = ({
  data = [],
  series = DEFAULT_SERIES,
  showGrid = true,
  height = 400,
  animated = true,
  showTrend = true,
  colors = DEFAULT_COLORS,
  title = "开箱消费趋势",
  description = "用户开箱行为和消费趋势分析",
  ...baseProps
}) => {
  // 计算趋势数据
  const trendData = useMemo(() => {
    if (!data || data.length < 2) {
      return null
    }

    const latest = data[data.length - 1]
    const previous = data[data.length - 2]

    const calculateTrend = (current: number, prev: number) => {
      if (prev === 0) return { direction: 'stable' as const, change: 0, percentage: 0 }
      const change = current - prev
      const percentage = (change / prev) * 100
      const direction = change > 0 ? 'up' as const : change < 0 ? 'down' as const : 'stable' as const
      return { direction, change, percentage }
    }

    return {
      boxCount: calculateTrend(latest.boxCount, previous.boxCount),
      boxAmount: calculateTrend(latest.boxAmount, previous.boxAmount),
      itemValue: calculateTrend(latest.itemValue, previous.itemValue),
      userCount: calculateTrend(latest.userCount, previous.userCount)
    }
  }, [data])

  // 计算统计数据
  const stats = useMemo(() => {
    const totalBoxes = data.reduce((sum, item) => sum + item.boxCount, 0)
    const totalAmount = data.reduce((sum, item) => sum + item.boxAmount, 0)
    const totalValue = data.reduce((sum, item) => sum + item.itemValue, 0)
    const avgUsers = data.length > 0 ? data.reduce((sum, item) => sum + item.userCount, 0) / data.length : 0
    
    // 计算投入产出比
    const roi = totalAmount > 0 ? ((totalValue - totalAmount) / totalAmount) * 100 : 0

    return {
      totalBoxes,
      totalAmount,
      totalValue,
      avgUsers: Math.round(avgUsers),
      roi
    }
  }, [data])

  // 自定义工具提示
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (!active || !payload || !payload.length) {
      return null
    }

    return (
      <div className="bg-background border border-border rounded-lg shadow-lg p-4 min-w-[200px]">
        <p className="font-medium text-sm mb-3">{label}</p>
        {payload.map((entry: any, index: number) => (
          <div key={index} className="flex items-center justify-between text-sm mb-1">
            <span className="flex items-center gap-2">
              <div 
                className="w-3 h-3 rounded-full" 
                style={{ backgroundColor: entry.color }}
              />
              {entry.name}
            </span>
            <span className="font-medium">
              {SERIES_CONFIG.find(s => s.key === entry.dataKey)?.formatter(entry.value) || entry.value}
            </span>
          </div>
        ))}
      </div>
    )
  }

  // 渲染统计信息
  const renderStats = () => (
    <div className="grid grid-cols-2 lg:grid-cols-5 gap-4 mb-4">
      <div className="text-center p-3 rounded-lg bg-muted/50">
        <div className="flex items-center justify-center gap-2 mb-1">
          <Package className="w-4 h-4 text-primary" />
          <p className="text-2xl font-bold text-primary">
            {formatters.number(stats.totalBoxes)}
          </p>
        </div>
        <p className="text-sm text-muted-foreground">总开箱数</p>
      </div>
      <div className="text-center p-3 rounded-lg bg-muted/50">
        <p className="text-2xl font-bold text-success">
          {formatters.currency(stats.totalAmount)}
        </p>
        <p className="text-sm text-muted-foreground">总消费</p>
      </div>
      <div className="text-center p-3 rounded-lg bg-muted/50">
        <p className="text-2xl font-bold text-warning">
          {formatters.currency(stats.totalValue)}
        </p>
        <p className="text-sm text-muted-foreground">物品价值</p>
      </div>
      <div className="text-center p-3 rounded-lg bg-muted/50">
        <p className="text-2xl font-bold text-info">
          {formatters.number(stats.avgUsers)}
        </p>
        <p className="text-sm text-muted-foreground">平均用户</p>
      </div>
      <div className="text-center p-3 rounded-lg bg-muted/50">
        <div className="flex items-center justify-center gap-1 mb-1">
          {stats.roi >= 0 ? (
            <TrendingUp className="w-4 h-4 text-green-500" />
          ) : (
            <TrendingDown className="w-4 h-4 text-red-500" />
          )}
          <p className={`text-2xl font-bold ${stats.roi >= 0 ? 'text-green-500' : 'text-red-500'}`}>
            {stats.roi.toFixed(1)}%
          </p>
        </div>
        <p className="text-sm text-muted-foreground">投入产出比</p>
      </div>
    </div>
  )

  // 渲染趋势指标
  const renderTrendIndicators = () => {
    if (!showTrend || !trendData) {
      return null
    }

    const indicators = [
      { key: 'boxCount', label: '开箱次数', color: colors.boxCount },
      { key: 'boxAmount', label: '开箱金额', color: colors.boxAmount },
      { key: 'itemValue', label: '物品价值', color: colors.itemValue },
      { key: 'userCount', label: '参与用户', color: colors.userCount }
    ]

    return (
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
        {indicators.map(({ key, label, color }) => {
          if (!series[key as keyof typeof series]) return null
          
          const trend = trendData[key as keyof typeof trendData]
          const TrendIcon = trend.direction === 'up' ? TrendingUp : 
                           trend.direction === 'down' ? TrendingDown : Package

          return (
            <div key={key} className="flex items-center gap-2 p-2 rounded-lg bg-muted/50">
              <div 
                className="w-3 h-3 rounded-full" 
                style={{ backgroundColor: color }}
              />
              <div className="flex-1 min-w-0">
                <p className="text-xs text-muted-foreground truncate">{label}</p>
                <div className="flex items-center gap-1">
                  <TrendIcon 
                    className={`w-3 h-3 ${
                      trend.direction === 'up' ? 'text-green-500' :
                      trend.direction === 'down' ? 'text-red-500' : 'text-gray-500'
                    }`}
                  />
                  <span className="text-xs font-medium">
                    {Math.abs(trend.percentage).toFixed(1)}%
                  </span>
                </div>
              </div>
            </div>
          )
        })}
      </div>
    )
  }

  // 渲染图表内容
  const renderChart = () => (
    <ChartContainer height={height}>
      <AreaChart data={data} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
        {showGrid && (
          <CartesianGrid 
            strokeDasharray="3 3" 
            stroke={CHART_COLORS.grid.light}
            opacity={0.3}
          />
        )}
        <XAxis 
          dataKey="date"
          tick={{ fontSize: 12 }}
          tickLine={{ stroke: CHART_COLORS.border.light }}
          axisLine={{ stroke: CHART_COLORS.border.light }}
        />
        <YAxis 
          yAxisId="count"
          orientation="left"
          tick={{ fontSize: 12 }}
          tickLine={{ stroke: CHART_COLORS.border.light }}
          axisLine={{ stroke: CHART_COLORS.border.light }}
          tickFormatter={formatters.number}
        />
        <YAxis 
          yAxisId="amount"
          orientation="right"
          tick={{ fontSize: 12 }}
          tickLine={{ stroke: CHART_COLORS.border.light }}
          axisLine={{ stroke: CHART_COLORS.border.light }}
          tickFormatter={formatters.currency}
        />
        <Tooltip content={<CustomTooltip />} />
        <Legend />
        
        {SERIES_CONFIG.map((config) => {
          const isVisible = series[config.key as keyof typeof series]
          if (!isVisible) return null

          return (
            <Area
              key={config.key}
              type="monotone"
              dataKey={config.key}
              name={config.name}
              yAxisId={config.yAxisId}
              stackId="1"
              stroke={colors[config.key as keyof typeof colors]}
              fill={colors[config.key as keyof typeof colors]}
              fillOpacity={0.6}
              animationDuration={animated ? CHART_ANIMATIONS.default.animationDuration : 0}
            />
          )
        })}
      </AreaChart>
    </ChartContainer>
  )

  return (
    <BaseChart
      title={title}
      description={description}
      height={height + (showTrend ? 250 : 150)}
      {...baseProps}
    >
      {renderStats()}
      {renderTrendIndicators()}
      {renderChart()}
    </BaseChart>
  )
}

export default BoxConsumptionArea
