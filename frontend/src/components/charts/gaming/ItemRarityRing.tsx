import React, { useMemo } from 'react'
import {
  <PERSON><PERSON>hart,
  Pie,
  Cell,

  Tooltip,
  Legend
} from 'recharts'
import { BaseChart, type BaseChartProps } from '../base/BaseChart'
import { ChartContainer } from '../base/ChartContainer'
import { formatters } from '../utils/chartDataTransform'
import { Gem, Star, Award, Shield, Crown } from 'lucide-react'

/**
 * 物品稀有度数据接口
 */
export interface ItemRarityData {
  /** 稀有度等级 */
  rarityLevel: number
  /** 稀有度名称 */
  rarityName: string
  /** 物品数量 */
  itemCount: number
  /** 总价值 */
  totalValue: number
  /** 平均价值 */
  avgValue: number
  /** 获得次数 */
  obtainCount: number
  /** 稀有度颜色 */
  color?: string
  /** 稀有度图标 */
  icon?: string
}

/**
 * 物品稀有度分布环形图属性接口
 */
export interface ItemRarityRingProps extends Omit<BaseChartProps, 'children'> {
  /** 稀有度数据 */
  data: ItemRarityData[]
  /** 显示模式 */
  mode?: 'count' | 'value' | 'obtain'
  /** 内圆半径 */
  innerRadius?: number
  /** 外圆半径 */
  outerRadius?: number
  /** 图表高度 */
  height?: number
  /** 是否显示标签 */
  showLabels?: boolean
  /** 是否显示百分比 */
  showPercentage?: boolean
  /** 是否启用动画 */
  animated?: boolean
  /** 自定义颜色配置 */
  colors?: string[]
}

/**
 * 默认稀有度颜色配置
 */
const DEFAULT_RARITY_COLORS = [
  '#94a3b8', // 普通 - 灰色
  '#22c55e', // 不常见 - 绿色
  '#3b82f6', // 稀有 - 蓝色
  '#a855f7', // 史诗 - 紫色
  '#f59e0b', // 传说 - 橙色
  '#ef4444', // 神话 - 红色
]

/**
 * 稀有度图标配置
 */
const RARITY_ICONS = {
  1: Gem,
  2: Star,
  3: Award,
  4: Shield,
  5: Crown,
  6: Crown
}

/**
 * 模式配置
 */
const MODE_CONFIG = {
  count: {
    dataKey: 'itemCount',
    name: '物品数量',
    formatter: formatters.number,
    unit: '个'
  },
  value: {
    dataKey: 'totalValue',
    name: '总价值',
    formatter: formatters.currency,
    unit: '元'
  },
  obtain: {
    dataKey: 'obtainCount',
    name: '获得次数',
    formatter: formatters.number,
    unit: '次'
  }
}

/**
 * 物品稀有度分布环形图组件
 */
export const ItemRarityRing: React.FC<ItemRarityRingProps> = ({
  data = [],
  mode = 'count',
  innerRadius = 80,
  outerRadius = 140,
  height = 400,
  showLabels = true,
  showPercentage = true,
  animated = true,
  colors = DEFAULT_RARITY_COLORS,
  title = "物品稀有度分布",
  description = "不同稀有度物品的分布情况",
  ...baseProps
}) => {
  // 获取当前模式配置
  const modeConfig = MODE_CONFIG[mode]

  // 处理图表数据
  const chartData = useMemo(() => {
    const total = data.reduce((sum, item) => sum + (item[modeConfig.dataKey as keyof ItemRarityData] as number), 0)
    
    return data.map((item, index) => {
      const value = (item[modeConfig.dataKey as keyof ItemRarityData] as number) || 0
      const percentage = total > 0 ? (value / total) * 100 : 0
      
      return {
        ...item,
        value,
        percentage,
        color: item.color || colors[index % colors.length],
        icon: RARITY_ICONS[item.rarityLevel as keyof typeof RARITY_ICONS] || Gem
      }
    }).sort((a, b) => a.rarityLevel - b.rarityLevel)
  }, [data, modeConfig, colors])

  // 计算统计数据
  const stats = useMemo(() => {
    const totalItems = data.reduce((sum, item) => sum + item.itemCount, 0)
    const totalValue = data.reduce((sum, item) => sum + item.totalValue, 0)
    const totalObtains = data.reduce((sum, item) => sum + item.obtainCount, 0)
    const avgValue = totalItems > 0 ? totalValue / totalItems : 0
    
    // 找出最稀有的物品类型
    const rarest = data.reduce((max, item) => 
      item.rarityLevel > max.rarityLevel ? item : max,
      data[0] || { rarityLevel: 0, rarityName: '', itemCount: 0, totalValue: 0, avgValue: 0, obtainCount: 0 }
    )
    
    // 找出最有价值的物品类型
    const mostValuable = data.reduce((max, item) => 
      item.totalValue > max.totalValue ? item : max,
      data[0] || { rarityLevel: 0, rarityName: '', itemCount: 0, totalValue: 0, avgValue: 0, obtainCount: 0 }
    )

    return {
      totalItems,
      totalValue,
      totalObtains,
      avgValue,
      rarest,
      mostValuable,
      rarityTypes: data.length
    }
  }, [data])

  // 自定义标签渲染
  const renderCustomLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent, name }: any) => {
    if (!showLabels || percent < 0.05) return null // 小于5%不显示标签

    const RADIAN = Math.PI / 180
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5
    const x = cx + radius * Math.cos(-midAngle * RADIAN)
    const y = cy + radius * Math.sin(-midAngle * RADIAN)

    return (
      <text 
        x={x} 
        y={y} 
        fill="white" 
        textAnchor={x > cx ? 'start' : 'end'} 
        dominantBaseline="central"
        fontSize={12}
        fontWeight="medium"
      >
        {showPercentage ? `${(percent * 100).toFixed(1)}%` : name}
      </text>
    )
  }

  // 自定义工具提示
  const CustomTooltip = ({ active, payload }: any) => {
    if (!active || !payload || !payload.length) {
      return null
    }

    const data = payload[0].payload
    const IconComponent = data.icon

    return (
      <div className="bg-background border border-border rounded-lg shadow-lg p-4 min-w-[200px]">
        <div className="flex items-center gap-2 mb-3">
          <div 
            className="w-3 h-3 rounded-full" 
            style={{ backgroundColor: data.color }}
          />
          <IconComponent className="w-4 h-4" style={{ color: data.color }} />
          <span className="font-medium text-sm">{data.rarityName}</span>
        </div>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-muted-foreground">物品数量:</span>
            <span className="font-medium">{formatters.number(data.itemCount)}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">总价值:</span>
            <span className="font-medium">{formatters.currency(data.totalValue)}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">平均价值:</span>
            <span className="font-medium">{formatters.currency(data.avgValue)}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">获得次数:</span>
            <span className="font-medium">{formatters.number(data.obtainCount)}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">占比:</span>
            <span className="font-medium">{data.percentage.toFixed(1)}%</span>
          </div>
        </div>
      </div>
    )
  }

  // 自定义图例
  const CustomLegend = ({ payload }: any) => {
    if (!payload || !payload.length) return null

    return (
      <div className="grid grid-cols-2 lg:grid-cols-3 gap-2 mt-4">
        {payload.map((entry: any, index: number) => {
          const data = chartData.find(item => item.rarityName === entry.value)
          const IconComponent = data?.icon || Gem
          
          return (
            <div key={index} className="flex items-center gap-2 p-2 rounded-lg bg-muted/50">
              <div 
                className="w-3 h-3 rounded-full" 
                style={{ backgroundColor: entry.color }}
              />
              <IconComponent className="w-4 h-4" style={{ color: entry.color }} />
              <span className="text-sm font-medium">{entry.value}</span>
            </div>
          )
        })}
      </div>
    )
  }

  // 渲染统计信息
  const renderStats = () => (
    <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
      <div className="text-center p-3 rounded-lg bg-muted/50">
        <p className="text-2xl font-bold text-primary">
          {formatters.number(stats.totalItems)}
        </p>
        <p className="text-sm text-muted-foreground">物品总数</p>
      </div>
      <div className="text-center p-3 rounded-lg bg-muted/50">
        <p className="text-2xl font-bold text-success">
          {formatters.currency(stats.totalValue)}
        </p>
        <p className="text-sm text-muted-foreground">总价值</p>
      </div>
      <div className="text-center p-3 rounded-lg bg-muted/50">
        <p className="text-2xl font-bold text-warning">
          {stats.rarest.rarityName}
        </p>
        <p className="text-sm text-muted-foreground">最稀有类型</p>
      </div>
      <div className="text-center p-3 rounded-lg bg-muted/50">
        <p className="text-2xl font-bold text-info">
          {formatters.currency(stats.avgValue)}
        </p>
        <p className="text-sm text-muted-foreground">平均价值</p>
      </div>
    </div>
  )

  // 渲染模式选择器
  const renderModeSelector = () => (
    <div className="flex items-center gap-2 mb-4">
      <span className="text-sm text-muted-foreground">显示模式:</span>
      <div className="flex gap-1">
        {Object.entries(MODE_CONFIG).map(([key, config]) => (
          <button
            key={key}
            className={`px-3 py-1 text-sm rounded-md transition-colors ${
              mode === key 
                ? 'bg-primary text-primary-foreground' 
                : 'bg-muted text-muted-foreground hover:bg-muted/80'
            }`}
            onClick={() => {
              // 这里应该通过props传递回调来改变mode
              console.log(`Switch to mode: ${key}`)
            }}
          >
            {config.name}
          </button>
        ))}
      </div>
    </div>
  )

  // 渲染中心统计
  const renderCenterStats = () => {
    const centerValue = chartData.reduce((sum, item) => sum + item.value, 0)
    
    return (
      <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
        <div className="text-center">
          <p className="text-3xl font-bold text-primary">
            {modeConfig.formatter(centerValue)}
          </p>
          <p className="text-sm text-muted-foreground">
            {modeConfig.name}
          </p>
        </div>
      </div>
    )
  }

  // 渲染图表内容
  const renderChart = () => (
    <div className="relative">
      <ChartContainer height={height}>
        <PieChart>
          <Pie
            data={chartData}
            cx="50%"
            cy="50%"
            labelLine={false}
            label={renderCustomLabel}
            outerRadius={outerRadius}
            innerRadius={innerRadius}
            fill="#8884d8"
            dataKey="value"
            animationBegin={0}
            animationDuration={animated ? 800 : 0}
          >
            {chartData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Pie>
          <Tooltip content={<CustomTooltip />} />
          <Legend content={<CustomLegend />} />
        </PieChart>
      </ChartContainer>
      {renderCenterStats()}
    </div>
  )

  return (
    <BaseChart
      title={title}
      description={description}
      height={height + 200}
      {...baseProps}
    >
      {renderStats()}
      {renderModeSelector()}
      {renderChart()}
    </BaseChart>
  )
}

export default ItemRarityRing
