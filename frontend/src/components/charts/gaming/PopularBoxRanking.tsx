import React, { useMemo } from 'react'
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer
} from 'recharts'
import { BaseChart, type BaseChartProps } from '../base/BaseChart'
import { ChartContainer } from '../base/ChartContainer'
import { CHART_COLORS, CHART_ANIMATIONS } from '../utils/chartTheme'
import { formatters } from '../utils/chartDataTransform'
import { Package, TrendingUp, Users, DollarSign, Trophy } from 'lucide-react'

/**
 * 热门箱子数据接口
 */
export interface PopularBoxData {
  /** 箱子ID */
  boxId: number
  /** 箱子名称 */
  boxName: string
  /** 开箱次数 */
  openCount: number
  /** 开箱收入 */
  revenue: number
  /** 参与用户数 */
  userCount: number
  /** 平均开箱价格 */
  avgPrice: number
  /** 热度分数 */
  popularityScore: number
  /** 箱子类型 */
  boxType?: string
  /** 上架时间 */
  launchTime?: string
}

/**
 * 热门箱子排行榜属性接口
 */
export interface PopularBoxRankingProps extends Omit<BaseChartProps, 'children'> {
  /** 热门箱子数据 */
  data: PopularBoxData[]
  /** 排序字段 */
  sortBy?: 'openCount' | 'revenue' | 'userCount' | 'popularityScore'
  /** 显示数量 */
  topN?: number
  /** 是否显示网格 */
  showGrid?: boolean
  /** 图表高度 */
  height?: number
  /** 是否启用动画 */
  animated?: boolean
  /** 柱状图颜色 */
  barColor?: string
  /** 是否显示排名 */
  showRanking?: boolean
}

/**
 * 排序字段配置
 */
const SORT_CONFIG = {
  openCount: { name: '开箱次数', formatter: formatters.number, color: CHART_COLORS.primary },
  revenue: { name: '开箱收入', formatter: formatters.currency, color: CHART_COLORS.success },
  userCount: { name: '参与用户', formatter: formatters.number, color: CHART_COLORS.warning },
  popularityScore: { name: '热度分数', formatter: formatters.number, color: CHART_COLORS.info }
}

/**
 * 热门箱子排行榜组件
 */
export const PopularBoxRanking: React.FC<PopularBoxRankingProps> = ({
  data = [],
  sortBy = 'openCount',
  topN = 10,
  showGrid = true,
  height = 400,
  animated = true,
  barColor,
  showRanking = true,
  title = "热门箱子排行榜",
  description = "最受欢迎的箱子开箱数据排名",
  ...baseProps
}) => {
  // 处理和排序数据
  const sortedData = useMemo(() => {
    const sorted = [...data]
      .sort((a, b) => b[sortBy] - a[sortBy])
      .slice(0, topN)
      .map((item, index) => ({
        ...item,
        rank: index + 1,
        displayName: item.boxName.length > 15 ? 
          `${item.boxName.substring(0, 15)}...` : 
          item.boxName
      }))
    
    return sorted
  }, [data, sortBy, topN])

  // 获取当前排序配置
  const sortConfig = SORT_CONFIG[sortBy]
  const currentBarColor = barColor || sortConfig.color

  // 计算统计数据
  const stats = useMemo(() => {
    const totalBoxes = data.length
    const totalOpens = data.reduce((sum, item) => sum + item.openCount, 0)
    const totalRevenue = data.reduce((sum, item) => sum + item.revenue, 0)
    const totalUsers = data.reduce((sum, item) => sum + item.userCount, 0)
    
    // 计算平均值
    const avgOpens = totalBoxes > 0 ? totalOpens / totalBoxes : 0
    const avgRevenue = totalBoxes > 0 ? totalRevenue / totalBoxes : 0
    
    // 找出最热门的箱子
    const topBox = sortedData[0]
    
    return {
      totalBoxes,
      totalOpens,
      totalRevenue,
      totalUsers,
      avgOpens,
      avgRevenue,
      topBox
    }
  }, [data, sortedData])

  // 自定义工具提示
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (!active || !payload || !payload.length) {
      return null
    }

    const data = payload[0].payload
    return (
      <div className="bg-background border border-border rounded-lg shadow-lg p-4 min-w-[200px]">
        <div className="flex items-center gap-2 mb-3">
          <div className="flex items-center gap-1">
            <Trophy className="w-4 h-4 text-warning" />
            <span className="font-bold text-sm">#{data.rank}</span>
          </div>
          <span className="font-medium text-sm">{data.boxName}</span>
        </div>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-muted-foreground">开箱次数:</span>
            <span className="font-medium">{formatters.number(data.openCount)}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">开箱收入:</span>
            <span className="font-medium">{formatters.currency(data.revenue)}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">参与用户:</span>
            <span className="font-medium">{formatters.number(data.userCount)}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">平均价格:</span>
            <span className="font-medium">{formatters.currency(data.avgPrice)}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">热度分数:</span>
            <span className="font-medium">{formatters.number(data.popularityScore)}</span>
          </div>
        </div>
      </div>
    )
  }

  // 自定义标签
  const CustomLabel = ({ x, y, width, value, payload }: any) => {
    if (!showRanking) return null
    
    return (
      <text 
        x={x + width / 2} 
        y={y - 5} 
        fill={CHART_COLORS.text.secondary}
        textAnchor="middle" 
        fontSize={12}
        fontWeight="bold"
      >
        #{payload.rank}
      </text>
    )
  }

  // 渲染统计信息
  const renderStats = () => (
    <div className="grid grid-cols-2 lg:grid-cols-5 gap-4 mb-4">
      <div className="text-center p-3 rounded-lg bg-muted/50">
        <div className="flex items-center justify-center gap-2 mb-1">
          <Package className="w-4 h-4 text-primary" />
          <p className="text-2xl font-bold text-primary">
            {formatters.number(stats.totalBoxes)}
          </p>
        </div>
        <p className="text-sm text-muted-foreground">箱子总数</p>
      </div>
      <div className="text-center p-3 rounded-lg bg-muted/50">
        <div className="flex items-center justify-center gap-2 mb-1">
          <TrendingUp className="w-4 h-4 text-success" />
          <p className="text-2xl font-bold text-success">
            {formatters.number(stats.totalOpens)}
          </p>
        </div>
        <p className="text-sm text-muted-foreground">总开箱次数</p>
      </div>
      <div className="text-center p-3 rounded-lg bg-muted/50">
        <div className="flex items-center justify-center gap-2 mb-1">
          <DollarSign className="w-4 h-4 text-warning" />
          <p className="text-2xl font-bold text-warning">
            {formatters.currency(stats.totalRevenue)}
          </p>
        </div>
        <p className="text-sm text-muted-foreground">总收入</p>
      </div>
      <div className="text-center p-3 rounded-lg bg-muted/50">
        <div className="flex items-center justify-center gap-2 mb-1">
          <Users className="w-4 h-4 text-info" />
          <p className="text-2xl font-bold text-info">
            {formatters.number(stats.totalUsers)}
          </p>
        </div>
        <p className="text-sm text-muted-foreground">参与用户</p>
      </div>
      <div className="text-center p-3 rounded-lg bg-muted/50">
        <div className="flex items-center justify-center gap-2 mb-1">
          <Trophy className="w-4 h-4 text-secondary" />
          <p className="text-lg font-bold text-secondary">
            {stats.topBox?.boxName.substring(0, 8) || 'N/A'}
          </p>
        </div>
        <p className="text-sm text-muted-foreground">最热门箱子</p>
      </div>
    </div>
  )

  // 渲染排序选择器
  const renderSortSelector = () => (
    <div className="flex items-center gap-2 mb-4">
      <span className="text-sm text-muted-foreground">排序方式:</span>
      <div className="flex gap-1">
        {Object.entries(SORT_CONFIG).map(([key, config]) => (
          <button
            key={key}
            className={`px-3 py-1 text-sm rounded-md transition-colors ${
              sortBy === key 
                ? 'bg-primary text-primary-foreground' 
                : 'bg-muted text-muted-foreground hover:bg-muted/80'
            }`}
            onClick={() => {
              // 这里应该通过props传递回调来改变sortBy
              console.log(`Switch to sort: ${key}`)
            }}
          >
            {config.name}
          </button>
        ))}
      </div>
    </div>
  )

  // 渲染图表内容
  const renderChart = () => (
    <ChartContainer height={height}>
      <BarChart 
        data={sortedData} 
        margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
        layout="horizontal"
      >
        {showGrid && (
          <CartesianGrid 
            strokeDasharray="3 3" 
            stroke={CHART_COLORS.grid.light}
            opacity={0.3}
          />
        )}
        <XAxis 
          type="number"
          tick={{ fontSize: 12 }}
          tickLine={{ stroke: CHART_COLORS.border.light }}
          axisLine={{ stroke: CHART_COLORS.border.light }}
          tickFormatter={sortConfig.formatter}
        />
        <YAxis 
          type="category"
          dataKey="displayName"
          tick={{ fontSize: 12 }}
          tickLine={{ stroke: CHART_COLORS.border.light }}
          axisLine={{ stroke: CHART_COLORS.border.light }}
          width={120}
        />
        <Tooltip content={<CustomTooltip />} />
        <Bar
          dataKey={sortBy}
          fill={currentBarColor}
          radius={[0, 4, 4, 0]}
          animationDuration={animated ? CHART_ANIMATIONS.default.animationDuration : 0}
          label={<CustomLabel />}
        />
      </BarChart>
    </ChartContainer>
  )

  return (
    <BaseChart
      title={title}
      description={description}
      height={height + 200}
      {...baseProps}
    >
      {renderStats()}
      {renderSortSelector()}
      {renderChart()}
    </BaseChart>
  )
}

export default PopularBoxRanking
