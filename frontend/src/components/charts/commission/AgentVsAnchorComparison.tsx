import React, { useMemo } from 'react'
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,

  Line,
  LineChart
} from 'recharts'
import { BaseChart, type BaseChartProps } from '../base/BaseChart'
import { ChartContainer } from '../base/ChartContainer'
import { CHART_COLORS, CHART_ANIMATIONS } from '../utils/chartTheme'
import { formatters } from '../utils/chartDataTransform'
import { Users, UserCheck, TrendingUp, DollarSign } from 'lucide-react'

/**
 * 代理vs主播对比数据接口
 */
export interface AgentVsAnchorData {
  /** 分类名称 */
  category: string
  /** 代理数据 */
  agent: {
    count: number
    totalRevenue: number
    avgRevenue: number
    commission: number
    avgCommissionRate: number
  }
  /** 主播数据 */
  anchor: {
    count: number
    totalRevenue: number
    avgRevenue: number
    commission: number
    avgCommissionRate: number
  }
}

/**
 * 代理vs主播收益对比图属性接口
 */
export interface AgentVsAnchorComparisonProps extends Omit<BaseChartProps, 'children'> {
  /** 对比数据 */
  data: AgentVsAnchorData[]
  /** 显示模式 */
  mode?: 'revenue' | 'commission' | 'count' | 'rate'
  /** 图表类型 */
  chartType?: 'bar' | 'line' | 'composed'
  /** 是否显示网格 */
  showGrid?: boolean
  /** 图表高度 */
  height?: number
  /** 是否启用动画 */
  animated?: boolean
  /** 自定义颜色配置 */
  colors?: {
    agent: string
    anchor: string
  }
}

/**
 * 默认颜色配置
 */
const DEFAULT_COLORS = {
  agent: CHART_COLORS.primary,
  anchor: CHART_COLORS.success
}

/**
 * 模式配置
 */
const MODE_CONFIG = {
  revenue: {
    agentKey: 'agent.totalRevenue',
    anchorKey: 'anchor.totalRevenue',
    agentName: '代理收入',
    anchorName: '主播收入',
    formatter: formatters.currency,
    unit: '元'
  },
  commission: {
    agentKey: 'agent.commission',
    anchorKey: 'anchor.commission',
    agentName: '代理佣金',
    anchorName: '主播佣金',
    formatter: formatters.currency,
    unit: '元'
  },
  count: {
    agentKey: 'agent.count',
    anchorKey: 'anchor.count',
    agentName: '代理数量',
    anchorName: '主播数量',
    formatter: formatters.number,
    unit: '人'
  },
  rate: {
    agentKey: 'agent.avgCommissionRate',
    anchorKey: 'anchor.avgCommissionRate',
    agentName: '代理费率',
    anchorName: '主播费率',
    formatter: (value: number) => `${value.toFixed(1)}%`,
    unit: '%'
  }
}

/**
 * 代理vs主播收益对比图组件
 */
export const AgentVsAnchorComparison: React.FC<AgentVsAnchorComparisonProps> = ({
  data = [],
  mode = 'revenue',
  chartType = 'bar',
  showGrid = true,
  height = 400,
  animated = true,
  colors = DEFAULT_COLORS,
  title = "代理vs主播收益对比",
  description = "代理和主播业绩数据对比分析",
  ...baseProps
}) => {
  // 获取当前模式配置
  const modeConfig = MODE_CONFIG[mode]

  // 处理图表数据
  const chartData = useMemo(() => {
    return data.map(item => ({
      category: item.category,
      agent: getNestedValue(item, modeConfig.agentKey),
      anchor: getNestedValue(item, modeConfig.anchorKey),
      agentCount: item.agent.count,
      anchorCount: item.anchor.count,
      agentRevenue: item.agent.totalRevenue,
      anchorRevenue: item.anchor.totalRevenue,
      agentCommission: item.agent.commission,
      anchorCommission: item.anchor.commission
    }))
  }, [data, modeConfig])

  // 计算统计数据
  const stats = useMemo(() => {
    const totalAgents = data.reduce((sum, item) => sum + item.agent.count, 0)
    const totalAnchors = data.reduce((sum, item) => sum + item.anchor.count, 0)
    const totalAgentRevenue = data.reduce((sum, item) => sum + item.agent.totalRevenue, 0)
    const totalAnchorRevenue = data.reduce((sum, item) => sum + item.anchor.totalRevenue, 0)
    const totalAgentCommission = data.reduce((sum, item) => sum + item.agent.commission, 0)
    const totalAnchorCommission = data.reduce((sum, item) => sum + item.anchor.commission, 0)

    const avgAgentRevenue = totalAgents > 0 ? totalAgentRevenue / totalAgents : 0
    const avgAnchorRevenue = totalAnchors > 0 ? totalAnchorRevenue / totalAnchors : 0

    return {
      totalAgents,
      totalAnchors,
      totalAgentRevenue,
      totalAnchorRevenue,
      totalAgentCommission,
      totalAnchorCommission,
      avgAgentRevenue,
      avgAnchorRevenue
    }
  }, [data])

  // 获取嵌套对象值的辅助函数
  function getNestedValue(obj: any, path: string): number {
    return path.split('.').reduce((current, key) => current?.[key], obj) || 0
  }

  // 自定义工具提示
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (!active || !payload || !payload.length) {
      return null
    }

    const originalData = data.find(item => item.category === label)
    if (!originalData) return null

    return (
      <div className="bg-background border border-border rounded-lg shadow-lg p-4 min-w-[250px]">
        <p className="font-medium text-sm mb-3">{label}</p>
        
        <div className="grid grid-cols-2 gap-4">
          {/* 代理数据 */}
          <div>
            <div className="flex items-center gap-2 mb-2">
              <div className="w-3 h-3 rounded-full" style={{ backgroundColor: colors.agent }} />
              <span className="font-medium text-sm">代理</span>
            </div>
            <div className="space-y-1 text-xs">
              <div className="flex justify-between">
                <span>数量:</span>
                <span>{formatters.number(originalData.agent.count)}</span>
              </div>
              <div className="flex justify-between">
                <span>收入:</span>
                <span>{formatters.currency(originalData.agent.totalRevenue)}</span>
              </div>
              <div className="flex justify-between">
                <span>佣金:</span>
                <span>{formatters.currency(originalData.agent.commission)}</span>
              </div>
              <div className="flex justify-between">
                <span>费率:</span>
                <span>{originalData.agent.avgCommissionRate.toFixed(1)}%</span>
              </div>
            </div>
          </div>

          {/* 主播数据 */}
          <div>
            <div className="flex items-center gap-2 mb-2">
              <div className="w-3 h-3 rounded-full" style={{ backgroundColor: colors.anchor }} />
              <span className="font-medium text-sm">主播</span>
            </div>
            <div className="space-y-1 text-xs">
              <div className="flex justify-between">
                <span>数量:</span>
                <span>{formatters.number(originalData.anchor.count)}</span>
              </div>
              <div className="flex justify-between">
                <span>收入:</span>
                <span>{formatters.currency(originalData.anchor.totalRevenue)}</span>
              </div>
              <div className="flex justify-between">
                <span>佣金:</span>
                <span>{formatters.currency(originalData.anchor.commission)}</span>
              </div>
              <div className="flex justify-between">
                <span>费率:</span>
                <span>{originalData.anchor.avgCommissionRate.toFixed(1)}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // 渲染统计信息
  const renderStats = () => (
    <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
      <div className="text-center p-3 rounded-lg bg-muted/50">
        <div className="flex items-center justify-center gap-2 mb-1">
          <Users className="w-4 h-4 text-primary" />
          <p className="text-2xl font-bold text-primary">
            {formatters.number(stats.totalAgents)}
          </p>
        </div>
        <p className="text-sm text-muted-foreground">代理总数</p>
      </div>
      <div className="text-center p-3 rounded-lg bg-muted/50">
        <div className="flex items-center justify-center gap-2 mb-1">
          <UserCheck className="w-4 h-4 text-success" />
          <p className="text-2xl font-bold text-success">
            {formatters.number(stats.totalAnchors)}
          </p>
        </div>
        <p className="text-sm text-muted-foreground">主播总数</p>
      </div>
      <div className="text-center p-3 rounded-lg bg-muted/50">
        <div className="flex items-center justify-center gap-2 mb-1">
          <DollarSign className="w-4 h-4 text-warning" />
          <p className="text-2xl font-bold text-warning">
            {formatters.currency(stats.avgAgentRevenue)}
          </p>
        </div>
        <p className="text-sm text-muted-foreground">代理平均收入</p>
      </div>
      <div className="text-center p-3 rounded-lg bg-muted/50">
        <div className="flex items-center justify-center gap-2 mb-1">
          <TrendingUp className="w-4 h-4 text-info" />
          <p className="text-2xl font-bold text-info">
            {formatters.currency(stats.avgAnchorRevenue)}
          </p>
        </div>
        <p className="text-sm text-muted-foreground">主播平均收入</p>
      </div>
    </div>
  )

  // 渲染模式选择器
  const renderModeSelector = () => (
    <div className="flex items-center gap-2 mb-4">
      <span className="text-sm text-muted-foreground">对比维度:</span>
      <div className="flex gap-1">
        {Object.entries(MODE_CONFIG).map(([key, config]) => (
          <button
            key={key}
            className={`px-3 py-1 text-sm rounded-md transition-colors ${
              mode === key 
                ? 'bg-primary text-primary-foreground' 
                : 'bg-muted text-muted-foreground hover:bg-muted/80'
            }`}
            onClick={() => {
              // 这里应该通过props传递回调来改变mode
              console.log(`Switch to mode: ${key}`)
            }}
          >
            {config.agentName.replace('代理', '')}
          </button>
        ))}
      </div>
    </div>
  )

  // 渲染图表内容
  const renderChart = () => {
    const commonProps = {
      data: chartData,
      margin: { top: 20, right: 30, left: 20, bottom: 5 }
    }

    const Chart = chartType === 'line' ? LineChart : BarChart

    return (
      <ChartContainer height={height}>
        <Chart {...commonProps}>
          {showGrid && (
            <CartesianGrid 
              strokeDasharray="3 3" 
              stroke={CHART_COLORS.grid.light}
              opacity={0.3}
            />
          )}
          <XAxis 
            dataKey="category"
            tick={{ fontSize: 12 }}
            tickLine={{ stroke: CHART_COLORS.border.light }}
            axisLine={{ stroke: CHART_COLORS.border.light }}
          />
          <YAxis 
            tick={{ fontSize: 12 }}
            tickLine={{ stroke: CHART_COLORS.border.light }}
            axisLine={{ stroke: CHART_COLORS.border.light }}
            tickFormatter={modeConfig.formatter}
          />
          <Tooltip content={<CustomTooltip />} />
          <Legend />
          
          {chartType === 'line' ? (
            <>
              <Line
                type="monotone"
                dataKey="agent"
                name={modeConfig.agentName}
                stroke={colors.agent}
                strokeWidth={2}
                dot={{ r: 4 }}
                activeDot={{ r: 6 }}
                animationDuration={animated ? CHART_ANIMATIONS.default.animationDuration : 0}
              />
              <Line
                type="monotone"
                dataKey="anchor"
                name={modeConfig.anchorName}
                stroke={colors.anchor}
                strokeWidth={2}
                dot={{ r: 4 }}
                activeDot={{ r: 6 }}
                animationDuration={animated ? CHART_ANIMATIONS.default.animationDuration : 0}
              />
            </>
          ) : (
            <>
              <Bar
                dataKey="agent"
                name={modeConfig.agentName}
                fill={colors.agent}
                radius={[4, 4, 0, 0]}
                animationDuration={animated ? CHART_ANIMATIONS.default.animationDuration : 0}
              />
              <Bar
                dataKey="anchor"
                name={modeConfig.anchorName}
                fill={colors.anchor}
                radius={[4, 4, 0, 0]}
                animationDuration={animated ? CHART_ANIMATIONS.default.animationDuration : 0}
              />
            </>
          )}
        </Chart>
      </ChartContainer>
    )
  }

  return (
    <BaseChart
      title={title}
      description={description}
      height={height + 200}
      {...baseProps}
    >
      {renderStats()}
      {renderModeSelector()}
      {renderChart()}
    </BaseChart>
  )
}

export default AgentVsAnchorComparison
