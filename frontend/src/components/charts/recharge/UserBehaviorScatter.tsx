import React, { useMemo } from 'react'
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>Axis,
  YAxis,
  CartesianGrid,
  Toolt<PERSON>,
  Legend,
  ResponsiveContainer,
  Cell
} from 'recharts'
import { BaseChart, type BaseChartProps } from '../base/BaseChart'
import { ChartContainer } from '../base/ChartContainer'
import { CHART_COLORS, CHART_ANIMATIONS } from '../utils/chartTheme'
import { formatters } from '../utils/chartDataTransform'
import { Users, DollarSign, TrendingUp, Star } from 'lucide-react'

/**
 * 用户行为散点数据接口
 */
export interface UserBehaviorScatterData {
  /** 用户ID */
  userId: number
  /** 用户名称 */
  userName?: string
  /** 充值次数 */
  rechargeCount: number
  /** 总充值金额 */
  totalAmount: number
  /** 平均充值金额 */
  avgAmount: number
  /** 用户等级 */
  userLevel: number
  /** 最后充值时间 */
  lastRechargeTime?: string
  /** 用户类型 */
  userType?: 'normal' | 'vip' | 'premium'
  /** 活跃度分数 */
  activityScore?: number
}

/**
 * 用户充值行为散点图属性接口
 */
export interface UserBehaviorScatterProps extends Omit<BaseChartProps, 'children'> {
  /** 用户行为数据 */
  data: UserBehaviorScatterData[]
  /** X轴数据字段 */
  xDataKey?: 'rechargeCount' | 'totalAmount' | 'avgAmount' | 'userLevel'
  /** Y轴数据字段 */
  yDataKey?: 'rechargeCount' | 'totalAmount' | 'avgAmount' | 'userLevel'
  /** 气泡大小字段 */
  sizeDataKey?: 'totalAmount' | 'rechargeCount' | 'activityScore'
  /** 颜色分类字段 */
  colorDataKey?: 'userType' | 'userLevel'
  /** 是否显示网格 */
  showGrid?: boolean
  /** 图表高度 */
  height?: number
  /** 是否启用动画 */
  animated?: boolean
  /** 自定义颜色配置 */
  colors?: Record<string, string>
  /** 气泡大小范围 */
  sizeRange?: [number, number]
}

/**
 * 默认颜色配置
 */
const DEFAULT_COLORS = {
  normal: CHART_COLORS.primary,
  vip: CHART_COLORS.warning,
  premium: CHART_COLORS.danger,
  level1: CHART_COLORS.palette[0],
  level2: CHART_COLORS.palette[1],
  level3: CHART_COLORS.palette[2],
  level4: CHART_COLORS.palette[3],
  level5: CHART_COLORS.palette[4]
}

/**
 * 字段配置
 */
const FIELD_CONFIG = {
  rechargeCount: { name: '充值次数', formatter: formatters.number, unit: '次' },
  totalAmount: { name: '总充值金额', formatter: formatters.currency, unit: '元' },
  avgAmount: { name: '平均充值金额', formatter: formatters.currency, unit: '元' },
  userLevel: { name: '用户等级', formatter: formatters.number, unit: '级' },
  activityScore: { name: '活跃度分数', formatter: formatters.number, unit: '分' }
}

/**
 * 用户充值行为散点图组件
 */
export const UserBehaviorScatter: React.FC<UserBehaviorScatterProps> = ({
  data = [],
  xDataKey = 'rechargeCount',
  yDataKey = 'totalAmount',
  sizeDataKey = 'totalAmount',
  colorDataKey = 'userType',
  showGrid = true,
  height = 400,
  animated = true,
  colors = DEFAULT_COLORS,
  sizeRange = [50, 400],
  title = "用户充值行为分析",
  description = "用户充值频次与金额关系分析",
  ...baseProps
}) => {
  // 处理散点图数据
  const scatterData = useMemo(() => {
    if (!data || data.length === 0) return []

    // 计算气泡大小的范围
    const sizeValues = data.map(item => item[sizeDataKey] || 0)
    const minSize = Math.min(...sizeValues)
    const maxSize = Math.max(...sizeValues)
    const sizeScale = (value: number) => {
      if (maxSize === minSize) return sizeRange[0]
      const ratio = (value - minSize) / (maxSize - minSize)
      return sizeRange[0] + ratio * (sizeRange[1] - sizeRange[0])
    }

    return data.map(item => ({
      x: item[xDataKey] || 0,
      y: item[yDataKey] || 0,
      z: sizeScale(item[sizeDataKey] || 0),
      color: getItemColor(item, colorDataKey, colors),
      ...item
    }))
  }, [data, xDataKey, yDataKey, sizeDataKey, colorDataKey, colors, sizeRange])

  // 获取项目颜色
  const getItemColor = (item: UserBehaviorScatterData, colorKey: string, colorMap: Record<string, string>) => {
    if (colorKey === 'userType') {
      return colorMap[item.userType || 'normal'] || colorMap.normal
    } else if (colorKey === 'userLevel') {
      return colorMap[`level${Math.min(item.userLevel, 5)}`] || colorMap.level1
    }
    return colorMap.normal
  }

  // 计算统计数据
  const stats = useMemo(() => {
    const totalUsers = data.length
    const totalAmount = data.reduce((sum, item) => sum + item.totalAmount, 0)
    const totalRecharges = data.reduce((sum, item) => sum + item.rechargeCount, 0)
    const avgAmount = totalUsers > 0 ? totalAmount / totalUsers : 0
    const avgRecharges = totalUsers > 0 ? totalRecharges / totalUsers : 0

    // 用户分层
    const userTypes = data.reduce((acc, item) => {
      const type = item.userType || 'normal'
      acc[type] = (acc[type] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    // 高价值用户（总充值金额前20%）
    const sortedByAmount = [...data].sort((a, b) => b.totalAmount - a.totalAmount)
    const highValueCount = Math.ceil(totalUsers * 0.2)
    const highValueUsers = sortedByAmount.slice(0, highValueCount)
    const highValueRevenue = highValueUsers.reduce((sum, user) => sum + user.totalAmount, 0)

    return {
      totalUsers,
      totalAmount,
      totalRecharges,
      avgAmount,
      avgRecharges,
      userTypes,
      highValueCount,
      highValueRevenue,
      highValuePercentage: totalAmount > 0 ? (highValueRevenue / totalAmount) * 100 : 0
    }
  }, [data])

  // 自定义工具提示
  const CustomTooltip = ({ active, payload }: any) => {
    if (!active || !payload || !payload.length) {
      return null
    }

    const data = payload[0].payload
    return (
      <div className="bg-background border border-border rounded-lg shadow-lg p-4 min-w-[200px]">
        <div className="flex items-center gap-2 mb-3">
          <div 
            className="w-3 h-3 rounded-full" 
            style={{ backgroundColor: data.color }}
          />
          <span className="font-medium text-sm">
            {data.userName || `用户${data.userId}`}
          </span>
        </div>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-muted-foreground">
              {FIELD_CONFIG[xDataKey].name}:
            </span>
            <span className="font-medium">
              {FIELD_CONFIG[xDataKey].formatter(data.x)}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">
              {FIELD_CONFIG[yDataKey].name}:
            </span>
            <span className="font-medium">
              {FIELD_CONFIG[yDataKey].formatter(data.y)}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">用户等级:</span>
            <span className="font-medium">{data.userLevel}级</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">用户类型:</span>
            <span className="font-medium">
              {data.userType === 'premium' ? '高级用户' : 
               data.userType === 'vip' ? 'VIP用户' : '普通用户'}
            </span>
          </div>
        </div>
      </div>
    )
  }

  // 渲染统计信息
  const renderStats = () => (
    <div className="grid grid-cols-2 lg:grid-cols-5 gap-4 mb-4">
      <div className="text-center p-3 rounded-lg bg-muted/50">
        <div className="flex items-center justify-center gap-2 mb-1">
          <Users className="w-4 h-4 text-primary" />
          <p className="text-2xl font-bold text-primary">
            {formatters.number(stats.totalUsers)}
          </p>
        </div>
        <p className="text-sm text-muted-foreground">总用户数</p>
      </div>
      <div className="text-center p-3 rounded-lg bg-muted/50">
        <div className="flex items-center justify-center gap-2 mb-1">
          <DollarSign className="w-4 h-4 text-success" />
          <p className="text-2xl font-bold text-success">
            {formatters.currency(stats.avgAmount)}
          </p>
        </div>
        <p className="text-sm text-muted-foreground">人均充值</p>
      </div>
      <div className="text-center p-3 rounded-lg bg-muted/50">
        <div className="flex items-center justify-center gap-2 mb-1">
          <TrendingUp className="w-4 h-4 text-warning" />
          <p className="text-2xl font-bold text-warning">
            {stats.avgRecharges.toFixed(1)}
          </p>
        </div>
        <p className="text-sm text-muted-foreground">平均充值次数</p>
      </div>
      <div className="text-center p-3 rounded-lg bg-muted/50">
        <div className="flex items-center justify-center gap-2 mb-1">
          <Star className="w-4 h-4 text-info" />
          <p className="text-2xl font-bold text-info">
            {formatters.number(stats.highValueCount)}
          </p>
        </div>
        <p className="text-sm text-muted-foreground">高价值用户</p>
      </div>
      <div className="text-center p-3 rounded-lg bg-muted/50">
        <p className="text-2xl font-bold text-secondary">
          {stats.highValuePercentage.toFixed(1)}%
        </p>
        <p className="text-sm text-muted-foreground">收入贡献占比</p>
      </div>
    </div>
  )

  // 渲染用户类型分布
  const renderUserTypeDistribution = () => (
    <div className="flex items-center justify-center gap-6 mb-4">
      {Object.entries(stats.userTypes).map(([type, count]) => (
        <div key={type} className="flex items-center gap-2">
          <div 
            className="w-3 h-3 rounded-full" 
            style={{ backgroundColor: colors[type] || colors.normal }}
          />
          <span className="text-sm text-muted-foreground">
            {type === 'premium' ? '高级用户' : 
             type === 'vip' ? 'VIP用户' : '普通用户'}: {count}
          </span>
        </div>
      ))}
    </div>
  )

  // 渲染图表内容
  const renderChart = () => (
    <ChartContainer height={height}>
      <ScatterChart margin={{ top: 20, right: 20, bottom: 20, left: 20 }}>
        {showGrid && (
          <CartesianGrid 
            strokeDasharray="3 3" 
            stroke={CHART_COLORS.grid.light}
            opacity={0.3}
          />
        )}
        <XAxis 
          type="number"
          dataKey="x"
          name={FIELD_CONFIG[xDataKey].name}
          tick={{ fontSize: 12 }}
          tickLine={{ stroke: CHART_COLORS.border.light }}
          axisLine={{ stroke: CHART_COLORS.border.light }}
          tickFormatter={FIELD_CONFIG[xDataKey].formatter}
        />
        <YAxis 
          type="number"
          dataKey="y"
          name={FIELD_CONFIG[yDataKey].name}
          tick={{ fontSize: 12 }}
          tickLine={{ stroke: CHART_COLORS.border.light }}
          axisLine={{ stroke: CHART_COLORS.border.light }}
          tickFormatter={FIELD_CONFIG[yDataKey].formatter}
        />
        <Tooltip content={<CustomTooltip />} />
        <Scatter
          data={scatterData}
          animationDuration={animated ? CHART_ANIMATIONS.default.animationDuration : 0}
        >
          {scatterData.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={entry.color} />
          ))}
        </Scatter>
      </ScatterChart>
    </ChartContainer>
  )

  return (
    <BaseChart
      title={title}
      description={description}
      height={height + 200}
      {...baseProps}
    >
      {renderStats()}
      {renderUserTypeDistribution()}
      {renderChart()}
    </BaseChart>
  )
}

export default UserBehaviorScatter
