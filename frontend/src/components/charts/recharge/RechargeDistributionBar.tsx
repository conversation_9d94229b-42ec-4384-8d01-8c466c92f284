import React, { useMemo } from 'react'
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts'
import { BaseChart, type BaseChartProps } from '../base/BaseChart'
import { ChartContainer } from '../base/ChartContainer'
import { CHART_COLORS, CHART_ANIMATIONS } from '../utils/chartTheme'
import { formatters } from '../utils/chartDataTransform'

/**
 * 充值分布数据接口
 */
export interface RechargeDistributionData {
  /** 金额区间 */
  range: string
  /** 订单数量 */
  orderCount: number
  /** 用户数量 */
  userCount: number
  /** 总金额 */
  totalAmount: number
  /** 平均金额 */
  avgAmount: number
  /** 最小金额 */
  minAmount?: number
  /** 最大金额 */
  maxAmount?: number
}

/**
 * 充值金额分布柱状图属性接口
 */
export interface RechargeDistributionBarProps extends Omit<BaseChartProps, 'children'> {
  /** 充值分布数据 */
  data: RechargeDistributionData[]
  /** 显示模式 */
  mode?: 'count' | 'amount' | 'users'
  /** 是否显示网格 */
  showGrid?: boolean
  /** 图表高度 */
  height?: number
  /** 柱状图颜色 */
  barColor?: string
  /** 是否启用动画 */
  animated?: boolean
  /** 是否显示数据标签 */
  showDataLabels?: boolean
}

/**
 * 充值金额分布柱状图组件
 */
export const RechargeDistributionBar: React.FC<RechargeDistributionBarProps> = ({
  data = [],
  mode = 'count',
  showGrid = true,
  height = 400,
  barColor = CHART_COLORS.primary,
  animated = true,
  showDataLabels = false,
  title = "充值金额分布",
  description = "用户充值金额区间分析",
  ...baseProps
}) => {
  // 计算统计数据
  const stats = useMemo(() => {
    const totalOrders = data.reduce((sum, item) => sum + item.orderCount, 0)
    const totalUsers = data.reduce((sum, item) => sum + item.userCount, 0)
    const totalAmount = data.reduce((sum, item) => sum + item.totalAmount, 0)
    const avgOrderAmount = totalOrders > 0 ? totalAmount / totalOrders : 0

    // 找出最受欢迎的充值区间
    const popularRange = data.reduce((max, item) => 
      item.orderCount > max.orderCount ? item : max, 
      data[0] || { range: '', orderCount: 0, userCount: 0, totalAmount: 0, avgAmount: 0 }
    )

    return {
      totalOrders,
      totalUsers,
      totalAmount,
      avgOrderAmount,
      popularRange: popularRange.range
    }
  }, [data])

  // 获取当前模式的数据键和格式化函数
  const getModeConfig = () => {
    switch (mode) {
      case 'amount':
        return {
          dataKey: 'totalAmount',
          name: '总金额',
          formatter: formatters.currency,
          color: CHART_COLORS.success
        }
      case 'users':
        return {
          dataKey: 'userCount',
          name: '用户数量',
          formatter: formatters.number,
          color: CHART_COLORS.warning
        }
      default:
        return {
          dataKey: 'orderCount',
          name: '订单数量',
          formatter: formatters.number,
          color: barColor
        }
    }
  }

  const modeConfig = getModeConfig()

  // 自定义工具提示
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (!active || !payload || !payload.length) {
      return null
    }

    const data = payload[0].payload
    return (
      <div className="bg-background border border-border rounded-lg shadow-lg p-4 min-w-[200px]">
        <p className="font-medium text-sm mb-3">{label}</p>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-muted-foreground">订单数量:</span>
            <span className="font-medium">{formatters.number(data.orderCount)}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">用户数量:</span>
            <span className="font-medium">{formatters.number(data.userCount)}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">总金额:</span>
            <span className="font-medium">{formatters.currency(data.totalAmount)}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">平均金额:</span>
            <span className="font-medium">{formatters.currency(data.avgAmount)}</span>
          </div>
        </div>
      </div>
    )
  }

  // 自定义数据标签
  const CustomLabel = ({ x, y, width, value }: any) => {
    if (!showDataLabels) return null
    return (
      <text 
        x={x + width / 2} 
        y={y - 5} 
        fill={CHART_COLORS.text.secondary}
        textAnchor="middle" 
        fontSize={12}
      >
        {modeConfig.formatter(value)}
      </text>
    )
  }

  // 渲染统计信息
  const renderStats = () => (
    <div className="grid grid-cols-2 lg:grid-cols-5 gap-4 mb-4">
      <div className="text-center p-3 rounded-lg bg-muted/50">
        <p className="text-2xl font-bold text-primary">
          {formatters.number(stats.totalOrders)}
        </p>
        <p className="text-sm text-muted-foreground">总订单数</p>
      </div>
      <div className="text-center p-3 rounded-lg bg-muted/50">
        <p className="text-2xl font-bold text-success">
          {formatters.number(stats.totalUsers)}
        </p>
        <p className="text-sm text-muted-foreground">充值用户</p>
      </div>
      <div className="text-center p-3 rounded-lg bg-muted/50">
        <p className="text-2xl font-bold text-warning">
          {formatters.currency(stats.totalAmount)}
        </p>
        <p className="text-sm text-muted-foreground">总充值额</p>
      </div>
      <div className="text-center p-3 rounded-lg bg-muted/50">
        <p className="text-2xl font-bold text-info">
          {formatters.currency(stats.avgOrderAmount)}
        </p>
        <p className="text-sm text-muted-foreground">平均订单</p>
      </div>
      <div className="text-center p-3 rounded-lg bg-muted/50">
        <p className="text-lg font-bold text-secondary">
          {stats.popularRange}
        </p>
        <p className="text-sm text-muted-foreground">热门区间</p>
      </div>
    </div>
  )

  // 渲染模式切换器
  const renderModeSelector = () => (
    <div className="flex items-center gap-2 mb-4">
      <span className="text-sm text-muted-foreground">显示模式:</span>
      <div className="flex gap-1">
        {[
          { key: 'count', label: '订单数量' },
          { key: 'amount', label: '总金额' },
          { key: 'users', label: '用户数量' }
        ].map(({ key, label }) => (
          <button
            key={key}
            className={`px-3 py-1 text-sm rounded-md transition-colors ${
              mode === key 
                ? 'bg-primary text-primary-foreground' 
                : 'bg-muted text-muted-foreground hover:bg-muted/80'
            }`}
            onClick={() => {
              // 这里应该通过props传递回调来改变mode
              console.log(`Switch to mode: ${key}`)
            }}
          >
            {label}
          </button>
        ))}
      </div>
    </div>
  )

  // 渲染图表内容
  const renderChart = () => (
    <ChartContainer height={height}>
      <BarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
        {showGrid && (
          <CartesianGrid 
            strokeDasharray="3 3" 
            stroke={CHART_COLORS.grid.light}
            opacity={0.3}
          />
        )}
        <XAxis 
          dataKey="range"
          tick={{ fontSize: 12 }}
          tickLine={{ stroke: CHART_COLORS.border.light }}
          axisLine={{ stroke: CHART_COLORS.border.light }}
          angle={-45}
          textAnchor="end"
          height={80}
        />
        <YAxis 
          tick={{ fontSize: 12 }}
          tickLine={{ stroke: CHART_COLORS.border.light }}
          axisLine={{ stroke: CHART_COLORS.border.light }}
          tickFormatter={modeConfig.formatter}
        />
        <Tooltip content={<CustomTooltip />} />
        <Legend />
        <Bar
          dataKey={modeConfig.dataKey}
          name={modeConfig.name}
          fill={modeConfig.color}
          radius={[4, 4, 0, 0]}
          animationDuration={animated ? CHART_ANIMATIONS.default.animationDuration : 0}
          label={<CustomLabel />}
        />
      </BarChart>
    </ChartContainer>
  )

  return (
    <BaseChart
      title={title}
      description={description}
      height={height + 200}
      {...baseProps}
    >
      {renderStats()}
      {renderModeSelector()}
      {renderChart()}
    </BaseChart>
  )
}

export default RechargeDistributionBar
