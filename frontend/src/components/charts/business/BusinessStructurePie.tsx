import React, { useMemo } from 'react'
import {
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  Tooltip,
  Legend
} from 'recharts'
import { BaseChart, type BaseChartProps } from '../base/BaseChart'
import { ChartContainer } from '../base/ChartContainer'
import { CHART_COLORS } from '../utils/chartTheme'
import { formatters } from '../utils/chartDataTransform'

/**
 * 业务结构数据接口
 */
export interface BusinessStructureData {
  /** 业务名称 */
  name: string
  /** 收入金额 */
  value: number
  /** 百分比 */
  percentage: number
  /** 自定义颜色 */
  color?: string
}

/**
 * 业务收入结构饼图属性接口
 */
export interface BusinessStructurePieProps extends Omit<BaseChartProps, 'children'> {
  /** 业务数据 */
  data: BusinessStructureData[]
  /** 是否显示标签 */
  showLabels?: boolean
  /** 是否显示百分比 */
  showPercentage?: boolean
  /** 内圆半径 */
  innerRadius?: number
  /** 外圆半径 */
  outerRadius?: number
  /** 图表高度 */
  height?: number
  /** 自定义颜色配置 */
  colors?: string[]
  /** 是否启用动画 */
  animated?: boolean
}

/**
 * 默认颜色配置
 */
const DEFAULT_COLORS = [
  CHART_COLORS.primary,
  CHART_COLORS.success,
  CHART_COLORS.warning,
  CHART_COLORS.danger,
  CHART_COLORS.info,
  CHART_COLORS.secondary
]

/**
 * 业务收入结构饼图组件
 */
export const BusinessStructurePie: React.FC<BusinessStructurePieProps> = ({
  data = [],
  showLabels = true,
  showPercentage = true,
  innerRadius = 60,
  outerRadius = 120,
  height = 400,
  colors = DEFAULT_COLORS,
  animated = true,
  title = "业务收入结构",
  description = "各业务线收入占比分析",
  ...baseProps
}) => {
  // 处理数据，添加颜色
  const chartData = useMemo(() => {
    return data.map((item, index) => ({
      ...item,
      color: item.color || colors[index % colors.length]
    }))
  }, [data, colors])

  // 计算总收入
  const totalRevenue = useMemo(() => {
    return data.reduce((sum, item) => sum + item.value, 0)
  }, [data])

  // 自定义标签渲染
  const renderCustomLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent, name }: any) => {
    if (!showLabels || percent < 0.05) return null // 小于5%不显示标签

    const RADIAN = Math.PI / 180
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5
    const x = cx + radius * Math.cos(-midAngle * RADIAN)
    const y = cy + radius * Math.sin(-midAngle * RADIAN)

    return (
      <text 
        x={x} 
        y={y} 
        fill="white" 
        textAnchor={x > cx ? 'start' : 'end'} 
        dominantBaseline="central"
        fontSize={12}
        fontWeight="medium"
      >
        {showPercentage ? `${(percent * 100).toFixed(1)}%` : name}
      </text>
    )
  }

  // 自定义工具提示
  const CustomTooltip = ({ active, payload }: any) => {
    if (!active || !payload || !payload.length) {
      return null
    }

    const data = payload[0].payload
    return (
      <div className="bg-background border border-border rounded-lg shadow-lg p-3 min-w-[150px]">
        <div className="flex items-center gap-2 mb-2">
          <div 
            className="w-3 h-3 rounded-full" 
            style={{ backgroundColor: data.color }}
          />
          <span className="font-medium text-sm">{data.name}</span>
        </div>
        <div className="space-y-1 text-sm">
          <div className="flex justify-between">
            <span className="text-muted-foreground">金额:</span>
            <span className="font-medium">{formatters.currency(data.value)}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">占比:</span>
            <span className="font-medium">{data.percentage.toFixed(1)}%</span>
          </div>
        </div>
      </div>
    )
  }

  // 自定义图例
  const CustomLegend = ({ payload }: any) => {
    if (!payload || !payload.length) return null

    return (
      <div className="flex flex-wrap justify-center gap-4 mt-4">
        {payload.map((entry: any, index: number) => (
          <div key={index} className="flex items-center gap-2">
            <div 
              className="w-3 h-3 rounded-full" 
              style={{ backgroundColor: entry.color }}
            />
            <span className="text-sm text-muted-foreground">{entry.value}</span>
          </div>
        ))}
      </div>
    )
  }

  // 渲染统计信息
  const renderStats = () => (
    <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
      <div className="text-center p-3 rounded-lg bg-muted/50">
        <p className="text-2xl font-bold text-primary">
          {formatters.currency(totalRevenue)}
        </p>
        <p className="text-sm text-muted-foreground">总收入</p>
      </div>
      <div className="text-center p-3 rounded-lg bg-muted/50">
        <p className="text-2xl font-bold text-success">
          {data.length}
        </p>
        <p className="text-sm text-muted-foreground">业务线</p>
      </div>
      <div className="text-center p-3 rounded-lg bg-muted/50">
        <p className="text-2xl font-bold text-warning">
          {data.length > 0 ? formatters.currency(Math.max(...data.map(d => d.value))) : '¥0'}
        </p>
        <p className="text-sm text-muted-foreground">最高收入</p>
      </div>
      <div className="text-center p-3 rounded-lg bg-muted/50">
        <p className="text-2xl font-bold text-info">
          {data.length > 0 ? formatters.currency(totalRevenue / data.length) : '¥0'}
        </p>
        <p className="text-sm text-muted-foreground">平均收入</p>
      </div>
    </div>
  )

  // 渲染图表内容
  const renderChart = () => (
    <ChartContainer height={height}>
      <PieChart>
        <Pie
          data={chartData}
          cx="50%"
          cy="50%"
          labelLine={false}
          label={renderCustomLabel}
          outerRadius={outerRadius}
          innerRadius={innerRadius}
          fill="#8884d8"
          dataKey="value"
          animationBegin={0}
          animationDuration={animated ? 800 : 0}
        >
          {chartData.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={entry.color} />
          ))}
        </Pie>
        <Tooltip content={<CustomTooltip />} />
        <Legend content={<CustomLegend />} />
      </PieChart>
    </ChartContainer>
  )

  return (
    <BaseChart
      title={title}
      description={description}
      height={height + 150}
      {...baseProps}
    >
      {renderStats()}
      {renderChart()}
    </BaseChart>
  )
}
export default BusinessStructurePie
