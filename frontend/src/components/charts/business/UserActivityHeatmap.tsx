import React, { useMemo } from 'react'
import { BaseChart, type BaseChartProps } from '../base/BaseChart'
import { CHART_COLORS } from '../utils/chartTheme'
import { formatters } from '../utils/chartDataTransform'
import { cn } from '@/utils'

/**
 * 热力图数据点接口
 */
export interface HeatmapDataPoint {
  /** 日期 */
  date: string
  /** 小时 (0-23) */
  hour: number
  /** 活跃用户数 */
  activeUsers: number
  /** 交易金额 */
  transactionAmount?: number
  /** 标签 */
  label?: string
}

/**
 * 用户活跃度热力图属性接口
 */
export interface UserActivityHeatmapProps extends Omit<BaseChartProps, 'children'> {
  /** 热力图数据 */
  data: HeatmapDataPoint[]
  /** 显示模式 */
  mode?: 'users' | 'amount'
  /** 图表高度 */
  height?: number
  /** 颜色配置 */
  colorScale?: {
    low: string
    medium: string
    high: string
  }
  /** 是否显示数值 */
  showValues?: boolean
  /** 最大值（用于颜色计算） */
  maxValue?: number
}

/**
 * 默认颜色配置
 */
const DEFAULT_COLOR_SCALE = {
  low: '#f0f9ff',
  medium: '#3b82f6',
  high: '#1e40af'
}

/**
 * 小时标签
 */
const HOUR_LABELS = Array.from({ length: 24 }, (_, i) => `${i.toString().padStart(2, '0')}:00`)

/**
 * 用户活跃度热力图组件
 */
export const UserActivityHeatmap: React.FC<UserActivityHeatmapProps> = ({
  data = [],
  mode = 'users',
  height = 400,
  colorScale = DEFAULT_COLOR_SCALE,
  showValues = false,
  maxValue,
  title = "用户活跃度热力图",
  description = "用户活跃时间分布分析",
  ...baseProps
}) => {
  // 处理数据，转换为矩阵格式
  const { heatmapMatrix, dates, maxVal, minVal } = useMemo(() => {
    if (!data || data.length === 0) {
      return { heatmapMatrix: [], dates: [], maxVal: 0, minVal: 0 }
    }

    // 获取所有唯一日期
    const uniqueDates = Array.from(new Set(data.map(d => d.date))).sort()
    
    // 创建矩阵
    const matrix: Array<Array<{ value: number; data: HeatmapDataPoint | null }>> = []
    
    let max = 0
    let min = Infinity
    
    uniqueDates.forEach(date => {
      const row: Array<{ value: number; data: HeatmapDataPoint | null }> = []
      
      for (let hour = 0; hour < 24; hour++) {
        const dataPoint = data.find(d => d.date === date && d.hour === hour)
        const value = dataPoint ? 
          (mode === 'users' ? dataPoint.activeUsers : dataPoint.transactionAmount || 0) : 0
        
        row.push({ value, data: dataPoint || null })
        
        if (value > max) max = value
        if (value < min && value > 0) min = value
      }
      
      matrix.push(row)
    })

    return {
      heatmapMatrix: matrix,
      dates: uniqueDates,
      maxVal: maxValue || max,
      minVal: min === Infinity ? 0 : min
    }
  }, [data, mode, maxValue])

  // 获取颜色强度
  const getColorIntensity = (value: number): number => {
    if (maxVal === 0) return 0
    return Math.min(value / maxVal, 1)
  }

  // 获取单元格颜色
  const getCellColor = (value: number): string => {
    if (value === 0) return '#f8fafc'
    
    const intensity = getColorIntensity(value)
    
    if (intensity < 0.3) {
      return colorScale.low
    } else if (intensity < 0.7) {
      // 在 low 和 medium 之间插值
      const factor = (intensity - 0.3) / 0.4
      return interpolateColor(colorScale.low, colorScale.medium, factor)
    } else {
      // 在 medium 和 high 之间插值
      const factor = (intensity - 0.7) / 0.3
      return interpolateColor(colorScale.medium, colorScale.high, factor)
    }
  }

  // 颜色插值函数
  const interpolateColor = (color1: string, color2: string, factor: number): string => {
    const hex1 = color1.replace('#', '')
    const hex2 = color2.replace('#', '')
    
    const r1 = parseInt(hex1.substr(0, 2), 16)
    const g1 = parseInt(hex1.substr(2, 2), 16)
    const b1 = parseInt(hex1.substr(4, 2), 16)
    
    const r2 = parseInt(hex2.substr(0, 2), 16)
    const g2 = parseInt(hex2.substr(2, 2), 16)
    const b2 = parseInt(hex2.substr(4, 2), 16)
    
    const r = Math.round(r1 + (r2 - r1) * factor)
    const g = Math.round(g1 + (g2 - g1) * factor)
    const b = Math.round(b1 + (b2 - b1) * factor)
    
    return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`
  }

  // 渲染统计信息
  const renderStats = () => {
    const totalValue = data.reduce((sum, d) => sum + (mode === 'users' ? d.activeUsers : d.transactionAmount || 0), 0)
    const avgValue = data.length > 0 ? totalValue / data.length : 0
    const peakHour = data.reduce((peak, d) => {
      const value = mode === 'users' ? d.activeUsers : d.transactionAmount || 0
      return value > (mode === 'users' ? peak.activeUsers : peak.transactionAmount || 0) ? d : peak
    }, data[0] || { hour: 0, activeUsers: 0, transactionAmount: 0 })

    return (
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
        <div className="text-center p-3 rounded-lg bg-muted/50">
          <p className="text-2xl font-bold text-primary">
            {mode === 'users' ? formatters.number(totalValue) : formatters.currency(totalValue)}
          </p>
          <p className="text-sm text-muted-foreground">
            {mode === 'users' ? '总活跃用户' : '总交易金额'}
          </p>
        </div>
        <div className="text-center p-3 rounded-lg bg-muted/50">
          <p className="text-2xl font-bold text-success">
            {mode === 'users' ? formatters.number(avgValue) : formatters.currency(avgValue)}
          </p>
          <p className="text-sm text-muted-foreground">平均值</p>
        </div>
        <div className="text-center p-3 rounded-lg bg-muted/50">
          <p className="text-2xl font-bold text-warning">
            {peakHour.hour.toString().padStart(2, '0')}:00
          </p>
          <p className="text-sm text-muted-foreground">高峰时段</p>
        </div>
        <div className="text-center p-3 rounded-lg bg-muted/50">
          <p className="text-2xl font-bold text-info">
            {mode === 'users' ? formatters.number(maxVal) : formatters.currency(maxVal)}
          </p>
          <p className="text-sm text-muted-foreground">峰值</p>
        </div>
      </div>
    )
  }

  // 渲染热力图
  const renderHeatmap = () => (
    <div className="space-y-4">
      {/* 时间轴 */}
      <div className="flex">
        <div className="w-20"></div>
        <div className="flex-1 grid grid-cols-24 gap-1">
          {HOUR_LABELS.map((hour, index) => (
            <div key={index} className="text-xs text-center text-muted-foreground">
              {index % 4 === 0 ? hour : ''}
            </div>
          ))}
        </div>
      </div>

      {/* 热力图主体 */}
      <div className="space-y-1">
        {heatmapMatrix.map((row, dateIndex) => (
          <div key={dateIndex} className="flex items-center">
            {/* 日期标签 */}
            <div className="w-20 text-sm text-muted-foreground text-right pr-2">
              {dates[dateIndex]}
            </div>
            
            {/* 热力图单元格 */}
            <div className="flex-1 grid grid-cols-24 gap-1">
              {row.map((cell, hourIndex) => (
                <div
                  key={hourIndex}
                  className={cn(
                    "aspect-square rounded-sm border border-border/20 flex items-center justify-center cursor-pointer transition-all hover:scale-110 hover:z-10 relative",
                    cell.value > 0 && "hover:shadow-lg"
                  )}
                  style={{ backgroundColor: getCellColor(cell.value) }}
                  title={cell.data ? 
                    `${cell.data.date} ${cell.data.hour.toString().padStart(2, '0')}:00\n${
                      mode === 'users' ? 
                        `活跃用户: ${formatters.number(cell.data.activeUsers)}` :
                        `交易金额: ${formatters.currency(cell.data.transactionAmount || 0)}`
                    }` : 
                    '无数据'
                  }
                >
                  {showValues && cell.value > 0 && (
                    <span className="text-xs font-medium text-white">
                      {cell.value > 999 ? `${Math.round(cell.value / 1000)}k` : cell.value}
                    </span>
                  )}
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* 颜色图例 */}
      <div className="flex items-center justify-center gap-4 pt-4">
        <span className="text-sm text-muted-foreground">低</span>
        <div className="flex gap-1">
          {Array.from({ length: 10 }, (_, i) => (
            <div
              key={i}
              className="w-4 h-4 rounded-sm"
              style={{ backgroundColor: getCellColor((maxVal * i) / 9) }}
            />
          ))}
        </div>
        <span className="text-sm text-muted-foreground">高</span>
      </div>
    </div>
  )

  return (
    <BaseChart
      title={title}
      description={description}
      height={height + 200}
      {...baseProps}
    >
      {renderStats()}
      {renderHeatmap()}
    </BaseChart>
  )
}

export default UserActivityHeatmap
