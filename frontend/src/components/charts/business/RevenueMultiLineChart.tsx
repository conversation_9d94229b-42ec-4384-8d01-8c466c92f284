import React, { useMemo } from 'react'
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts'
import { BaseChart, type BaseChartProps } from '../base/BaseChart'
import { ChartContainer } from '../base/ChartContainer'
import { CHART_COLORS, CHART_ANIMATIONS } from '../utils/chartTheme'
import { formatters } from '../utils/chartDataTransform'
import { TrendingUp, TrendingDown, Minus } from 'lucide-react'

/**
 * 多维度收入数据接口
 */
export interface RevenueDataPoint {
  /** 日期 */
  date: string
  /** 充值收入 */
  rechargeRevenue: number
  /** 开箱收入 */
  boxRevenue: number
  /** 佣金支出 */
  commissionExpense: number
  /** 净收入 */
  netRevenue: number
  /** 时间戳 */
  timestamp?: number
}

/**
 * 多维度收入趋势图属性接口
 */
export interface RevenueMultiLineChartProps extends Omit<BaseChartProps, 'children'> {
  /** 收入数据 */
  data: RevenueDataPoint[]
  /** 显示的数据线配置 */
  lines?: {
    rechargeRevenue?: boolean
    boxRevenue?: boolean
    commissionExpense?: boolean
    netRevenue?: boolean
  }
  /** 是否显示数据点 */
  showDots?: boolean
  /** 是否显示网格 */
  showGrid?: boolean
  /** 是否显示趋势指标 */
  showTrend?: boolean
  /** 图表高度 */
  height?: number
  /** 自定义颜色配置 */
  colors?: {
    rechargeRevenue?: string
    boxRevenue?: string
    commissionExpense?: string
    netRevenue?: string
  }
}

/**
 * 默认线条配置
 */
const DEFAULT_LINES = {
  rechargeRevenue: true,
  boxRevenue: true,
  commissionExpense: true,
  netRevenue: true
}

/**
 * 默认颜色配置
 */
const DEFAULT_COLORS = {
  rechargeRevenue: CHART_COLORS.primary,
  boxRevenue: CHART_COLORS.success,
  commissionExpense: CHART_COLORS.warning,
  netRevenue: CHART_COLORS.secondary
}

/**
 * 线条配置
 */
const LINE_CONFIG = [
  {
    key: 'rechargeRevenue',
    name: '充值收入',
    strokeWidth: 2,
    dot: { r: 4 },
    activeDot: { r: 6 }
  },
  {
    key: 'boxRevenue',
    name: '开箱收入',
    strokeWidth: 2,
    dot: { r: 4 },
    activeDot: { r: 6 }
  },
  {
    key: 'commissionExpense',
    name: '佣金支出',
    strokeWidth: 2,
    dot: { r: 4 },
    activeDot: { r: 6 },
    strokeDasharray: '5 5'
  },
  {
    key: 'netRevenue',
    name: '净收入',
    strokeWidth: 3,
    dot: { r: 5 },
    activeDot: { r: 7 }
  }
]

/**
 * 多维度收入趋势图组件
 */
export const RevenueMultiLineChart: React.FC<RevenueMultiLineChartProps> = ({
  data = [],
  lines = DEFAULT_LINES,
  showDots = true,
  showGrid = true,
  showTrend = true,
  height = 400,
  colors = DEFAULT_COLORS,
  title = "收入趋势分析",
  description = "多维度收入数据趋势对比",
  ...baseProps
}) => {
  // 计算趋势数据
  const trendData = useMemo(() => {
    if (!data || data.length < 2) {
      return null
    }

    const latest = data[data.length - 1]
    const previous = data[data.length - 2]

    const calculateTrend = (current: number, prev: number) => {
      if (prev === 0) return { direction: 'stable' as const, change: 0, percentage: 0 }
      const change = current - prev
      const percentage = (change / prev) * 100
      const direction = change > 0 ? 'up' as const : change < 0 ? 'down' as const : 'stable' as const
      return { direction, change, percentage }
    }

    return {
      rechargeRevenue: calculateTrend(latest.rechargeRevenue, previous.rechargeRevenue),
      boxRevenue: calculateTrend(latest.boxRevenue, previous.boxRevenue),
      commissionExpense: calculateTrend(latest.commissionExpense, previous.commissionExpense),
      netRevenue: calculateTrend(latest.netRevenue, previous.netRevenue)
    }
  }, [data])

  // 自定义工具提示
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (!active || !payload || !payload.length) {
      return null
    }

    return (
      <div className="bg-background border border-border rounded-lg shadow-lg p-3 min-w-[200px]">
        <p className="font-medium text-sm mb-2">{label}</p>
        {payload.map((entry: any, index: number) => (
          <div key={index} className="flex items-center justify-between text-sm mb-1">
            <span className="flex items-center gap-2">
              <div 
                className="w-3 h-3 rounded-full" 
                style={{ backgroundColor: entry.color }}
              />
              {entry.name}
            </span>
            <span className="font-medium">
              {formatters.currency(entry.value)}
            </span>
          </div>
        ))}
      </div>
    )
  }

  // 渲染趋势指标
  const renderTrendIndicators = () => {
    if (!showTrend || !trendData) {
      return null
    }

    const indicators = [
      { key: 'rechargeRevenue', label: '充值收入', color: colors.rechargeRevenue },
      { key: 'boxRevenue', label: '开箱收入', color: colors.boxRevenue },
      { key: 'commissionExpense', label: '佣金支出', color: colors.commissionExpense },
      { key: 'netRevenue', label: '净收入', color: colors.netRevenue }
    ]

    return (
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
        {indicators.map(({ key, label, color }) => {
          const trend = trendData[key as keyof typeof trendData]
          const TrendIcon = trend.direction === 'up' ? TrendingUp : 
                           trend.direction === 'down' ? TrendingDown : Minus

          return (
            <div key={key} className="flex items-center gap-2 p-2 rounded-lg bg-muted/50">
              <div 
                className="w-3 h-3 rounded-full" 
                style={{ backgroundColor: color }}
              />
              <div className="flex-1 min-w-0">
                <p className="text-xs text-muted-foreground truncate">{label}</p>
                <div className="flex items-center gap-1">
                  <TrendIcon 
                    className={`w-3 h-3 ${
                      trend.direction === 'up' ? 'text-green-500' :
                      trend.direction === 'down' ? 'text-red-500' : 'text-gray-500'
                    }`}
                  />
                  <span className="text-xs font-medium">
                    {Math.abs(trend.percentage).toFixed(1)}%
                  </span>
                </div>
              </div>
            </div>
          )
        })}
      </div>
    )
  }

  // 渲染图表内容
  const renderChart = () => (
    <ChartContainer height={height}>
      <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        {showGrid && (
          <CartesianGrid 
            strokeDasharray="3 3" 
            stroke={CHART_COLORS.grid.light}
            opacity={0.3}
          />
        )}
        <XAxis 
          dataKey="date"
          tick={{ fontSize: 12 }}
          tickLine={{ stroke: CHART_COLORS.border.light }}
          axisLine={{ stroke: CHART_COLORS.border.light }}
        />
        <YAxis 
          tick={{ fontSize: 12 }}
          tickLine={{ stroke: CHART_COLORS.border.light }}
          axisLine={{ stroke: CHART_COLORS.border.light }}
          tickFormatter={(value) => formatters.currency(value)}
        />
        <Tooltip content={<CustomTooltip />} />
        <Legend 
          wrapperStyle={{ paddingTop: '20px' }}
          iconType="line"
        />
        
        {LINE_CONFIG.map((config) => {
          const isVisible = lines[config.key as keyof typeof lines]
          if (!isVisible) return null

          return (
            <Line
              key={config.key}
              type="monotone"
              dataKey={config.key}
              name={config.name}
              stroke={colors[config.key as keyof typeof colors]}
              strokeWidth={config.strokeWidth}
              strokeDasharray={config.strokeDasharray}
              dot={showDots ? config.dot : false}
              activeDot={config.activeDot}
              animationDuration={CHART_ANIMATIONS.default.animationDuration}
              animationEasing={CHART_ANIMATIONS.default.animationEasing}
            />
          )
        })}
      </LineChart>
    </ChartContainer>
  )

  return (
    <BaseChart
      title={title}
      description={description}
      height={height + (showTrend ? 100 : 0)}
      {...baseProps}
    >
      {renderTrendIndicators()}
      {renderChart()}
    </BaseChart>
  )
}

export default RevenueMultiLineChart
